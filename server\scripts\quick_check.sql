-- =====================================================
--  快速检查 (Quick Check)
-- =====================================================

SELECT '=== 快速检查现有函数和触发器 ===' as section;

-- 查看现有的自定义函数
SELECT
    ROUTINE_NAME as function_name,
    ROUTINE_TYPE as type,
    CREATED as created_time,
    LAST_ALTERED as last_modified,
    ROUTINE_COMMENT as comment
FROM information_schema.ROUTINES
WHERE ROUTINE_SCHEMA = 'identify'
AND ROUTINE_TYPE = 'FUNCTION'
ORDER BY ROUTINE_NAME;

-- 查看现有的触发器
SELECT
    TRIGGER_NAME as trigger_name,
    EVENT_MANIPULATION as event_type,
    EVENT_OBJECT_TABLE as table_name,
    CREATED as created_time
FROM information_schema.TRIGGERS
WHERE TRIGGER_SCHEMA = 'identify'
ORDER BY TRIGGER_NAME;

-- 查看现有的存储过程
SELECT
    ROUTINE_NAME as procedure_name,
    ROUTINE_TYPE as type,
    CREATED as created_time,
    LAST_ALTERED as last_modified,
    ROUTINE_COMMENT as comment
FROM information_schema.ROUTINES
WHERE ROUTINE_SCHEMA = 'identify'
AND ROUTINE_TYPE = 'PROCEDURE'
ORDER BY ROUTINE_NAME;

-- 查看现有的视图
SELECT
    TABLE_NAME as view_name,
    VIEW_DEFINITION as definition_preview
FROM information_schema.VIEWS
WHERE TABLE_SCHEMA = 'identify'
ORDER BY TABLE_NAME;

-- 测试关键函数是否正常工作
SELECT '=== 测试关键函数 ===' as section;

-- 测试fn_clean_follower_name函数
SELECT
    'fn_clean_follower_name' as function_name,
    fn_clean_follower_name('测试用户 13812345678') as test_result,
    '应该返回: 测试用户' as expected;

-- 测试fn_check_order_created_duplicate函数
SELECT
    'fn_check_order_created_duplicate' as function_name,
    fn_check_order_created_duplicate('TEST_ORDER', '测试跟单员') as test_result,
    '0=未发送过，1=已发送过' as description;

-- 测试fn_check_order_shipped_duplicate函数
SELECT
    'fn_check_order_shipped_duplicate' as function_name,
    fn_check_order_shipped_duplicate('TEST_ORDER', '测试跟单员') as test_result,
    '0=未发送过，1=已发送过' as description;

-- 测试fn_check_delayed_shipping_duplicate函数
SELECT
    'fn_check_delayed_shipping_duplicate' as function_name,
    fn_check_delayed_shipping_duplicate('TEST_ORDER', '测试跟单员', 3) as test_result,
    '0=未发送过，1=已发送过' as description;

-- 查看当前队列状态
SELECT '=== 当前队列状态 ===' as section;

SELECT
    status,
    COUNT(*) as count,
    notification_type
FROM sms_notification_queue
GROUP BY status, notification_type
ORDER BY status, notification_type;

-- 查看最近的队列任务
SELECT
    id,
    order_no,
    notification_type,
    follower_name,
    JSON_EXTRACT(template_params, '$.follower') as template_follower,
    status,
    error_msg,
    created_at
FROM sms_notification_queue
ORDER BY created_at DESC
LIMIT 5;
