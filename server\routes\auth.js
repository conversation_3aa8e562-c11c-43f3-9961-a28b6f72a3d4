/**
 * 用户认证路由
 * 处理登录、注册、token验证等功能
 */

const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const crypto = require('crypto');

/**
 * 用户登录
 * POST /api/auth/login
 */
router.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        // 验证输入参数
        if (!username || !password) {
            return res.status(400).json({
                success: false,
                message: '用户名和密码不能为空'
            });
        }

        // 查询用户信息
        const sql = `
            SELECT id, username, password, factory_name, status 
            FROM Factory_Login 
            WHERE username = ? AND status = 1
        `;
        
        const users = await query(sql, [username.trim()]);

        if (users.length === 0) {
            return res.status(401).json({
                success: false,
                message: '用户名或密码错误'
            });
        }

        const user = users[0];

        // 验证密码（这里简化处理，实际应该使用加密密码）
        if (user.password !== password.trim()) {
            return res.status(401).json({
                success: false,
                message: '用户名或密码错误'
            });
        }

        // 生成简单的token（实际应该使用JWT）
        const token = generateToken(user.id, user.username);

        // 返回登录成功信息
        res.json({
            success: true,
            message: '登录成功',
            data: {
                username: user.username,
                factory_name: user.factory_name,
                token: token,
                expires_in: 2592000 // 1个月 (30天 * 24小时 * 60分钟 * 60秒)
            }
        });

        console.log(`✅ 用户登录成功: ${user.username} (${user.factory_name})`);

    } catch (error) {
        console.error('❌ 登录失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 跟单员用户登录
 * POST /api/auth/follower-login
 */
router.post('/follower-login', async (req, res) => {
    try {
        const { username, password } = req.body;

        // 验证输入参数
        if (!username || !password) {
            return res.status(400).json({
                success: false,
                message: '用户名和密码不能为空'
            });
        }

        // 查询跟单员用户信息
        const sql = `
            SELECT id, username, password, follower_name, position, status
            FROM follower_login
            WHERE username = ? AND status = 1
        `;

        const users = await query(sql, [username.trim()]);

        if (users.length === 0) {
            return res.status(401).json({
                success: false,
                message: '用户名或密码错误'
            });
        }

        const user = users[0];

        // 验证密码（这里简化处理，实际应该使用加密密码）
        if (user.password !== password.trim()) {
            return res.status(401).json({
                success: false,
                message: '用户名或密码错误'
            });
        }

        // 生成简单的token（实际应该使用JWT）
        const token = generateToken(user.id, user.username);

        // 返回登录成功信息
        res.json({
            success: true,
            message: '登录成功',
            data: {
                username: user.username,
                follower_name: user.follower_name,
                position: user.position || 1, // 默认为跟单员
                token: token,
                expires_in: 2592000 // 1个月 (30天 * 24小时 * 60分钟 * 60秒)
            }
        });

        console.log(`✅ 跟单员用户登录成功: ${user.username} (${user.follower_name})`);

    } catch (error) {
        console.error('❌ 跟单员登录失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 验证token中间件
 */
function verifyToken(req, res, next) {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
            success: false,
            message: '未提供有效的认证token'
        });
    }

    const token = authHeader.substring(7); // 移除 "Bearer " 前缀
    
    try {
        // 解析token（简化处理）
        const decoded = parseToken(token);
        req.user = decoded;
        next();
    } catch (error) {
        return res.status(401).json({
            success: false,
            message: 'token无效或已过期'
        });
    }
}

/**
 * 获取用户信息
 * GET /api/auth/profile
 */
router.get('/profile', verifyToken, async (req, res) => {
    try {
        const userId = req.user.id;

        const sql = `
            SELECT id, username, factory_name, created_at 
            FROM Factory_Login 
            WHERE id = ? AND status = 1
        `;
        
        const users = await query(sql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const user = users[0];

        res.json({
            success: true,
            data: {
                id: user.id,
                username: user.username,
                factory_name: user.factory_name,
                created_at: user.created_at
            }
        });

    } catch (error) {
        console.error('❌ 获取用户信息失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 生成token（简化版本）
 */
function generateToken(userId, username, extraPayload = {}) {
    const payload = {
        id: userId,
        username: username,
        timestamp: Date.now(),
        ...extraPayload
    };
    
    // 简单的base64编码（实际应该使用JWT）
    return Buffer.from(JSON.stringify(payload)).toString('base64');
}

/**
 * 解析token（简化版本）
 */
function parseToken(token) {
    try {
        const payload = JSON.parse(Buffer.from(token, 'base64').toString());
        
        // 检查token是否过期（1个月）
        const now = Date.now();
        const tokenAge = now - payload.timestamp;
        const maxAge = 30 * 24 * 60 * 60 * 1000; // 1个月 (30天 * 24小时 * 60分钟 * 60秒)
        
        if (tokenAge > maxAge) {
            throw new Error('Token已过期');
        }
        
        return payload;
    } catch (error) {
        throw new Error('Token解析失败');
    }
}

/**
 * 跟单员获取工厂访问token
 * POST /api/auth/follower-factory-access
 */
router.post('/follower-factory-access', verifyToken, async (req, res) => {
    try {
        const { factory_name } = req.body;
        const username = req.user.username;

        // 验证输入参数
        if (!factory_name) {
            return res.status(400).json({
                success: false,
                message: '工厂名称不能为空'
            });
        }

        // 验证是否为跟单员用户
        const followerSql = `
            SELECT id, username, follower_name
            FROM follower_login
            WHERE username = ? AND status = 1
        `;

        const followers = await query(followerSql, [username]);

        if (followers.length === 0) {
            return res.status(404).json({
                success: false,
                message: '跟单员用户不存在'
            });
        }

        // 验证工厂是否存在
        const factorySql = `
            SELECT id, username, factory_name
            FROM Factory_Login
            WHERE factory_name = ? AND status = 1
            LIMIT 1
        `;

        const factories = await query(factorySql, [factory_name]);

        if (factories.length === 0) {
            return res.status(404).json({
                success: false,
                message: '指定工厂不存在'
            });
        }

        const factory = factories[0];

        // 生成临时访问token，包含工厂信息
        const token = generateToken(factory.id, factory.username, {
            factory_name: factory.factory_name,
            follower_access: true,
            follower_username: username
        });

        console.log('✅ 跟单员获取工厂访问token成功:', {
            follower: username,
            factory: factory_name
        });

        res.json({
            success: true,
            message: '获取工厂访问权限成功',
            data: {
                username: factory.username,
                factory_name: factory.factory_name,
                token: token,
                expires_in: 24 * 60 * 60, // 24小时
                follower_access: true
            }
        });

    } catch (error) {
        console.error('❌ 跟单员获取工厂访问token失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 跟单员修改密码
 * POST /api/auth/follower-change-password
 */
router.post('/follower-change-password', verifyToken, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        const userId = req.user.id;
        const username = req.user.username;

        // 验证输入参数
        if (!currentPassword || !newPassword) {
            return res.status(400).json({
                success: false,
                message: '当前密码和新密码不能为空'
            });
        }

        // 验证新密码长度
        if (newPassword.trim().length < 6) {
            return res.status(400).json({
                success: false,
                message: '新密码长度不能少于6位'
            });
        }

        // 验证是否为跟单员用户
        const followerSql = `
            SELECT id, username, password, follower_name
            FROM follower_login
            WHERE id = ? AND username = ? AND status = 1
        `;

        const followers = await query(followerSql, [userId, username]);

        if (followers.length === 0) {
            return res.status(404).json({
                success: false,
                message: '跟单员用户不存在'
            });
        }

        const follower = followers[0];

        // 验证当前密码
        if (follower.password !== currentPassword.trim()) {
            return res.status(401).json({
                success: false,
                message: '当前密码错误'
            });
        }

        // 更新密码
        const updateSql = `
            UPDATE follower_login
            SET password = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ? AND username = ?
        `;

        const updateResult = await query(updateSql, [newPassword.trim(), userId, username]);

        if (updateResult.affectedRows === 0) {
            return res.status(500).json({
                success: false,
                message: '密码更新失败'
            });
        }

        console.log(`✅ 跟单员 ${username} 密码修改成功`);

        res.json({
            success: true,
            message: '密码修改成功'
        });

    } catch (error) {
        console.error('❌ 跟单员修改密码失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 密码加密（MD5简化版本）
 */
function encryptPassword(password) {
    return crypto.createHash('md5').update(password).digest('hex');
}

module.exports = router;
module.exports.verifyToken = verifyToken;
