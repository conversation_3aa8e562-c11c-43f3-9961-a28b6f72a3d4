-- =====================================================
-- 修复跟单员姓名格式问题
-- 解决阿里云短信模板变量规范问题
-- =====================================================

USE identify;

-- =====================================================
-- 1. 创建清理跟单员姓名的函数
-- =====================================================

DELIMITER $$

DROP FUNCTION IF EXISTS fn_clean_follower_name$$

CREATE FUNCTION fn_clean_follower_name(
    p_follower_name VARCHAR(100)
) RETURNS VARCHAR(50)
DETERMINISTIC
BEGIN
    DECLARE v_clean_name VARCHAR(100);
    
    -- 检查输入
    IF p_follower_name IS NULL OR TRIM(p_follower_name) = '' THEN
        RETURN '跟单员';
    END IF;
    
    SET v_clean_name = p_follower_name;
    
    -- 去除手机号码（11位数字，以1开头）
    SET v_clean_name = TRIM(REGEXP_REPLACE(v_clean_name, '\\s*1[3-9][0-9]{9}\\s*', ''));
    
    -- 去除其他数字和特殊字符
    SET v_clean_name = REGEXP_REPLACE(v_clean_name, '[0-9\\-\\(\\)\\[\\]]', '');
    
    -- 去除多余的空格
    SET v_clean_name = TRIM(REGEXP_REPLACE(v_clean_name, '\\s+', ' '));
    
    -- 限制长度
    IF CHAR_LENGTH(v_clean_name) > 10 THEN
        SET v_clean_name = LEFT(v_clean_name, 10);
    END IF;
    
    -- 如果清理后为空，使用默认值
    IF v_clean_name IS NULL OR TRIM(v_clean_name) = '' THEN
        SET v_clean_name = '跟单员';
    END IF;
    
    RETURN v_clean_name;
END$$

DELIMITER ;

-- =====================================================
-- 2. 测试清理函数
-- =====================================================

SELECT '=== 测试跟单员姓名清理函数 ===' as section;

-- 测试各种格式的跟单员姓名
SELECT 
    '李静东 13735339044' as original_name,
    fn_clean_follower_name('李静东 13735339044') as cleaned_name
UNION ALL
SELECT 
    '张三(13812345678)' as original_name,
    fn_clean_follower_name('张三(13812345678)') as cleaned_name
UNION ALL
SELECT 
    '王五-15987654321' as original_name,
    fn_clean_follower_name('王五-15987654321') as cleaned_name
UNION ALL
SELECT 
    '赵六' as original_name,
    fn_clean_follower_name('赵六') as cleaned_name
UNION ALL
SELECT 
    '' as original_name,
    fn_clean_follower_name('') as cleaned_name
UNION ALL
SELECT 
    NULL as original_name,
    fn_clean_follower_name(NULL) as cleaned_name;

-- =====================================================
-- 3. 检查现有数据中的跟单员姓名格式
-- =====================================================

SELECT '=== 检查现有跟单员姓名格式 ===' as section;

-- 检查follower_login表中的姓名格式
SELECT 
    follower_name as original_name,
    fn_clean_follower_name(follower_name) as cleaned_name,
    CASE 
        WHEN follower_name REGEXP '1[3-9][0-9]{9}' THEN '包含手机号'
        WHEN follower_name REGEXP '[0-9]' THEN '包含数字'
        ELSE '格式正常'
    END as format_status
FROM follower_login 
WHERE status = 1
ORDER BY format_status DESC, follower_name;

-- 检查sms_notification_queue表中的跟单员姓名
SELECT 
    follower_name as original_name,
    fn_clean_follower_name(follower_name) as cleaned_name,
    COUNT(*) as task_count,
    MAX(created_at) as latest_task
FROM sms_notification_queue 
WHERE status IN ('pending', 'failed')
GROUP BY follower_name, fn_clean_follower_name(follower_name)
ORDER BY task_count DESC;

-- =====================================================
-- 4. 修复现有队列中的template_params
-- =====================================================

SELECT '=== 修复队列中的template_params ===' as section;

-- 备份当前的template_params
CREATE TABLE IF NOT EXISTS sms_queue_backup_follower_fix AS
SELECT 
    id, 
    order_no, 
    follower_name,
    template_params, 
    created_at,
    'follower_name_fix' as backup_reason
FROM sms_notification_queue 
WHERE status IN ('pending', 'failed')
AND JSON_EXTRACT(template_params, '$.follower') REGEXP '1[3-9][0-9]{9}';

-- 显示备份的记录数
SELECT CONCAT('已备份 ', COUNT(*), ' 条包含手机号的记录') as backup_result
FROM sms_queue_backup_follower_fix;

-- 修复order_created类型的template_params
UPDATE sms_notification_queue 
SET template_params = JSON_SET(
    template_params,
    '$.follower', fn_clean_follower_name(follower_name)
)
WHERE notification_type = 'order_created' 
AND status IN ('pending', 'failed')
AND JSON_EXTRACT(template_params, '$.follower') REGEXP '1[3-9][0-9]{9}';

-- 修复order_shipped类型的template_params
UPDATE sms_notification_queue 
SET template_params = JSON_SET(
    template_params,
    '$.follower', fn_clean_follower_name(follower_name)
)
WHERE notification_type = 'order_shipped' 
AND status IN ('pending', 'failed')
AND JSON_EXTRACT(template_params, '$.follower') REGEXP '1[3-9][0-9]{9}';

-- 修复delayed_shipping类型的template_params
UPDATE sms_notification_queue 
SET template_params = JSON_SET(
    template_params,
    '$.follower', fn_clean_follower_name(follower_name)
)
WHERE notification_type = 'delayed_shipping' 
AND status IN ('pending', 'failed')
AND JSON_EXTRACT(template_params, '$.follower') REGEXP '1[3-9][0-9]{9}';

-- =====================================================
-- 5. 重置因模板参数错误失败的任务
-- =====================================================

SELECT '=== 重置失败的任务 ===' as section;

-- 重置因为模板参数错误失败的任务
UPDATE sms_notification_queue 
SET status = 'pending',
    retry_count = 0,
    error_msg = NULL,
    updated_at = NOW()
WHERE status = 'failed' 
AND (
    error_msg LIKE '%TEMPLATE_PARAMS_ILLEGAL%' OR
    error_msg LIKE '%变量规范%' OR
    error_msg LIKE '%用户昵称%'
)
AND retry_count < 3;

-- 显示重置的任务数量
SELECT ROW_COUNT() as reset_tasks_count;

-- =====================================================
-- 6. 更新触发器使用清理函数
-- =====================================================

SELECT '=== 更新触发器 ===' as section;

-- 删除旧触发器
DROP TRIGGER IF EXISTS tr_shipping_detail_sms_final;
DROP TRIGGER IF EXISTS tr_img_info_sms_final;

-- 重新创建使用清理函数的触发器
DELIMITER $$

CREATE TRIGGER tr_shipping_detail_sms_final
    AFTER INSERT ON shipping_detail
    FOR EACH ROW
BEGIN
    DECLARE v_phone VARCHAR(20) DEFAULT NULL;
    DECLARE v_allowed TINYINT DEFAULT 0;
    DECLARE v_exists INT DEFAULT 0;
    DECLARE v_clean_follower VARCHAR(50);
    DECLARE v_error_count INT DEFAULT 0;
    
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION 
    BEGIN
        SET v_error_count = v_error_count + 1;
        INSERT IGNORE INTO trigger_error_logs (
            trigger_name, table_name, record_id, error_message, created_at
        ) VALUES (
            'tr_shipping_detail_sms_final', 'shipping_detail', NEW.id, 
            CONCAT('SMS trigger error for order: ', NEW.order_no), NOW()
        );
    END;
    
    IF NEW.follower IS NOT NULL AND TRIM(NEW.follower) != '' THEN
        
        -- 清理跟单员姓名
        SET v_clean_follower = fn_clean_follower_name(NEW.follower);
        
        SELECT phone, allowed INTO v_phone, v_allowed
        FROM follower_login
        WHERE follower_name = NEW.follower
        AND status = 1
        LIMIT 1;
        
        IF v_allowed = 1 AND v_phone IS NOT NULL AND TRIM(v_phone) != '' THEN
            
            SET v_exists = fn_check_order_created_duplicate(NEW.order_no, NEW.follower);
            
            IF v_exists = 0 THEN
                
                INSERT IGNORE INTO sms_notification_queue (
                    order_no, factory_name, follower_name, phone_number, 
                    notification_type, template_code, template_params, status,
                    created_at, updated_at
                ) VALUES (
                    NEW.order_no, 
                    NEW.receiver, 
                    NEW.follower, 
                    v_phone,
                    'order_created',
                    'SMS_493305115',
                    JSON_OBJECT(
                        'follower', v_clean_follower,
                        'orderNo', NEW.order_no,
                        'customerName', NEW.receiver,
                        'orderTime', DATE_FORMAT(NEW.created_at, '%Y/%m/%d %H:%i:%s')
                    ),
                    'pending',
                    NOW(),
                    NOW()
                );
                
                INSERT IGNORE INTO delayed_shipping_tracking (
                    order_no, factory_name, follower_name,
                    order_created_at, is_shipped,
                    created_at, updated_at
                ) VALUES (
                    NEW.order_no,
                    NEW.receiver,
                    NEW.follower,
                    NEW.created_at,
                    0,
                    NOW(),
                    NOW()
                );
                
            END IF;
        END IF;
    END IF;
END$$

CREATE TRIGGER tr_img_info_sms_final
    AFTER INSERT ON img_info
    FOR EACH ROW
BEGIN
    DECLARE v_follower VARCHAR(50) DEFAULT NULL;
    DECLARE v_phone VARCHAR(20) DEFAULT NULL;
    DECLARE v_allowed TINYINT DEFAULT 0;
    DECLARE v_exists INT DEFAULT 0;
    DECLARE v_clean_follower VARCHAR(50);
    DECLARE v_error_count INT DEFAULT 0;
    
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION 
    BEGIN
        SET v_error_count = v_error_count + 1;
        INSERT IGNORE INTO trigger_error_logs (
            trigger_name, table_name, record_id, error_message, created_at
        ) VALUES (
            'tr_img_info_sms_final', 'img_info', NEW.id,
            CONCAT('SMS trigger error for order: ', NEW.order_number), NOW()
        );
    END;
    
    SELECT follower INTO v_follower
    FROM shipping_detail 
    WHERE order_no = NEW.order_number 
    AND receiver = NEW.factory_name
    LIMIT 1;
    
    IF v_follower IS NOT NULL AND TRIM(v_follower) != '' THEN
        
        -- 清理跟单员姓名
        SET v_clean_follower = fn_clean_follower_name(v_follower);
        
        SELECT phone, allowed INTO v_phone, v_allowed
        FROM follower_login 
        WHERE follower_name = v_follower 
        AND status = 1
        LIMIT 1;
        
        IF v_allowed = 1 AND v_phone IS NOT NULL AND TRIM(v_phone) != '' THEN
            
            SET v_exists = fn_check_order_shipped_duplicate(NEW.order_number, v_follower);
            
            IF v_exists = 0 THEN
                
                INSERT IGNORE INTO sms_notification_queue (
                    order_no, factory_name, follower_name, phone_number, 
                    notification_type, template_code, template_params, status,
                    created_at, updated_at
                ) VALUES (
                    NEW.order_number, 
                    NEW.factory_name, 
                    v_follower, 
                    v_phone,
                    'order_shipped',
                    'SMS_493260128',
                    JSON_OBJECT(
                        'follower', v_clean_follower,
                        'orderNo', NEW.order_number,
                        'customerName', NEW.factory_name
                    ),
                    'pending',
                    NOW(),
                    NOW()
                );
                
                UPDATE delayed_shipping_tracking
                SET is_shipped = 1,
                    shipped_at = NOW(),
                    updated_at = NOW()
                WHERE order_no = NEW.order_number 
                AND factory_name = NEW.factory_name;
                
            END IF;
        END IF;
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 7. 验证修复结果
-- =====================================================

SELECT '=== 验证修复结果 ===' as section;

-- 检查修复后的template_params
SELECT 
    id,
    order_no,
    notification_type,
    follower_name,
    JSON_EXTRACT(template_params, '$.follower') as template_follower,
    status
FROM sms_notification_queue 
WHERE status IN ('pending', 'failed')
ORDER BY created_at DESC
LIMIT 10;

-- 检查是否还有包含手机号的template_params
SELECT 
    COUNT(*) as remaining_issues
FROM sms_notification_queue 
WHERE status IN ('pending', 'failed')
AND JSON_EXTRACT(template_params, '$.follower') REGEXP '1[3-9][0-9]{9}';

SELECT '✅ 跟单员姓名格式修复完成！' as result;

