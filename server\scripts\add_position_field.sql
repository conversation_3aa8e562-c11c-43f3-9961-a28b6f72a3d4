-- 为follower_login表添加position字段的SQL脚本
-- 执行时间：需要在生产环境中谨慎执行

-- 1. 添加position字段
ALTER TABLE `follower_login` 
ADD COLUMN `position` tinyint NULL DEFAULT 1 COMMENT '用户身份：1=跟单员，2=订坯人员，3=跟单员和订坯员' 
AFTER `phone`;

-- 2. 为position字段添加索引
ALTER TABLE `follower_login` 
ADD INDEX `idx_position`(`position` ASC);

-- 3. 更新现有数据，将所有现有用户设置为跟单员（position = 1）
UPDATE `follower_login` SET `position` = 1 WHERE `position` IS NULL;

-- 4. 查看更新结果
SELECT id, username, follower_name, position, status FROM `follower_login` WHERE status = 1; 