/**
 * 短信通知服务类
 * 替换微信订阅消息系统，使用阿里云短信服务
 */

const AliyunSMSService = require('./aliyunSMSService');
const DuplicateCheckService = require('./duplicateCheckService');
const { query, transaction } = require('../config/database');

class SMSNotificationService {
    constructor(aliyunSMSService = null) {
        // 支持依赖注入，避免重复创建实例
        this.smsService = aliyunSMSService || new AliyunSMSService();
        this.duplicateCheckService = new DuplicateCheckService();

        // 短信模板配置
        this.templates = {
            order_created: {
                templateCode: 'SMS_493305115',
                signName: '智联金',
                description: '坯布订单生成消息'
            },
            delayed_shipping: {
                templateCode: 'SMS_493420157',
                signName: '智联金',
                description: '坯布发货超时通知'
            },
            order_shipped: {
                templateCode: 'SMS_493260128',
                signName: '智联金',
                description: '坯布发货通知'
            }
        };

        // 只在没有注入依赖时输出日志
        if (!aliyunSMSService) {
            console.log('📱 短信通知服务初始化完成');
        }
    }

    /**
     * 发送订单生成通知
     * @param {string} orderNo 订单号
     * @param {string} factoryName 工厂名称
     * @param {string} followerName 跟单员姓名
     * @returns {Promise<Object>} 发送结果
     */
    async sendOrderCreatedNotification(orderNo, factoryName, followerName) {
        try {
            console.log(`📤 准备发送订单生成通知: ${orderNo} -> ${followerName}`);

            // 使用统一的重复检查服务
            const alreadySent = await this.duplicateCheckService.checkOrderCreatedDuplicate(orderNo, followerName);
            if (alreadySent) {
                console.log(`⚠️ 订单 ${orderNo} 的生成通知已发送过，跳过重复发送`);
                return { success: false, message: '该订单的生成通知已发送过' };
            }

            // 获取跟单员手机号
            const phoneNumber = await this.getFollowerPhone(followerName);
            if (!phoneNumber) {
                console.log(`⚠️ 跟单员 ${followerName} 未配置手机号，跳过发送`);
                return { success: false, message: '跟单员未配置手机号' };
            }

            // 获取订单详细信息
            const orderInfo = await this.getOrderInfo(orderNo, factoryName);
            if (!orderInfo) {
                console.log(`⚠️ 未找到订单信息: ${orderNo}`);
                return { success: false, message: '未找到订单信息' };
            }

            // 构建模板参数
            const templateParams = {
                follower: this.cleanFollowerName(followerName),
                orderNo: orderNo,
                customerName: factoryName,
                orderTime: this.formatDateTime(orderInfo.created_at)
            };

            const template = this.templates.order_created;
            
            // 发送短信
            const result = await this.smsService.sendSMS(
                phoneNumber,
                template.signName,
                template.templateCode,
                templateParams,
                `order_created_${orderNo}_${Date.now()}`
            );

            // 使用事务记录日志和更新相关状态
            await transaction(async (connection) => {
                // 记录短信日志
                await this.logSMSNotificationWithConnection(
                    connection, phoneNumber, orderNo, followerName, 'order_created',
                    template.templateCode, templateParams, result
                );
            });

            if (result.success) {
                console.log(`✅ 订单生成通知发送成功: ${orderNo} -> ${followerName}`);
            } else {
                console.error(`❌ 订单生成通知发送失败: ${orderNo} -> ${followerName}`, result.error);
            }

            return result;

        } catch (error) {
            console.error('❌ 发送订单生成通知异常:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 发送延迟发货通知
     * @param {string} orderNo 订单号
     * @param {string} factoryName 工厂名称
     * @param {string} followerName 跟单员姓名
     * @param {number} delayDays 延迟天数
     * @returns {Promise<Object>} 发送结果
     */
    async sendDelayedShippingNotification(orderNo, factoryName, followerName, delayDays) {
        try {
            // 使用统一的重复检查服务
            const alreadySent = await this.duplicateCheckService.checkDelayedShippingDuplicate(orderNo, followerName, delayDays);
            if (alreadySent) {
                console.log(`⚠️ 订单 ${orderNo} 的 ${delayDays} 天延迟通知已发送过，跳过重复发送`);
                return { success: false, message: `该订单的 ${delayDays} 天延迟通知已发送过` };
            }

            // 获取跟单员手机号
            const phoneNumber = await this.getFollowerPhone(followerName);
            if (!phoneNumber) {
                return { success: false, message: '跟单员未配置手机号' };
            }

            // 获取订单详细信息
            const orderInfo = await this.getOrderInfo(orderNo, factoryName);
            if (!orderInfo) {
                return { success: false, message: '未找到订单信息' };
            }

            // 构建模板参数
            const templateParams = {
                follower: this.cleanFollowerName(followerName),
                orderNo: orderNo,
                delayDays: delayDays.toString(),
                customerName: factoryName,
                orderTime: this.formatDateTime(orderInfo.created_at)
            };

            const template = this.templates.delayed_shipping;
            
            // 发送短信
            const result = await this.smsService.sendSMS(
                phoneNumber,
                template.signName,
                template.templateCode,
                templateParams,
                `delayed_shipping_${orderNo}_${delayDays}d_${Date.now()}`
            );

            // 使用事务记录日志
            await transaction(async (connection) => {
                // 记录短信日志
                await this.logSMSNotificationWithConnection(
                    connection, phoneNumber, orderNo, followerName, 'delayed_shipping',
                    template.templateCode, templateParams, result
                );
            });

            if (result.success) {
                console.log(`✅ 延迟发货通知发送成功: ${orderNo} -> ${followerName} (${delayDays}天)`);
            } else {
                console.error(`❌ 延迟发货通知发送失败: ${orderNo} -> ${followerName}`, result.error);
            }

            return result;

        } catch (error) {
            console.error('❌ 发送延迟发货通知异常:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 发送订单发货通知
     * @param {string} orderNo 订单号
     * @param {string} factoryName 工厂名称
     * @param {string} followerName 跟单员姓名
     * @returns {Promise<Object>} 发送结果
     */
    async sendOrderShippedNotification(orderNo, factoryName, followerName) {
        try {
            console.log(`📤 准备发送订单发货通知: ${orderNo} -> ${followerName}`);

            // 使用统一的重复检查服务
            const alreadySent = await this.duplicateCheckService.checkOrderShippedDuplicate(orderNo, followerName);
            if (alreadySent) {
                console.log(`⚠️ 订单 ${orderNo} 的发货通知已发送过，跳过重复发送`);
                return { success: false, message: '该订单的发货通知已发送过' };
            }

            // 获取跟单员手机号
            const phoneNumber = await this.getFollowerPhone(followerName);
            if (!phoneNumber) {
                console.log(`⚠️ 跟单员 ${followerName} 未配置手机号，跳过发送`);
                return { success: false, message: '跟单员未配置手机号' };
            }

            // 构建模板参数
            const templateParams = {
                follower: this.cleanFollowerName(followerName),
                orderNo: orderNo,
                customerName: factoryName
            };

            const template = this.templates.order_shipped;
            
            // 发送短信
            const result = await this.smsService.sendSMS(
                phoneNumber,
                template.signName,
                template.templateCode,
                templateParams,
                `order_shipped_${orderNo}_${Date.now()}`
            );

            // 使用事务记录日志和更新发货状态
            await transaction(async (connection) => {
                // 记录短信日志
                await this.logSMSNotificationWithConnection(
                    connection, phoneNumber, orderNo, followerName, 'order_shipped',
                    template.templateCode, templateParams, result
                );

                // 更新延迟发货跟踪状态
                await this.markOrderAsShippedWithConnection(connection, orderNo, factoryName);
            });

            if (result.success) {
                console.log(`✅ 订单发货通知发送成功: ${orderNo} -> ${followerName}`);
            } else {
                console.error(`❌ 订单发货通知发送失败: ${orderNo} -> ${followerName}`, result.error);
            }

            return result;

        } catch (error) {
            console.error('❌ 发送订单发货通知异常:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取跟单员手机号
     * @param {string} followerName 跟单员姓名
     * @returns {Promise<string|null>} 手机号
     */
    async getFollowerPhone(followerName) {
        try {
            const sql = `
                SELECT phone 
                FROM follower_login 
                WHERE follower_name = ? AND status = 1 AND phone IS NOT NULL
            `;
            const result = await query(sql, [followerName]);
            return result.length > 0 ? result[0].phone : null;
        } catch (error) {
            console.error('❌ 获取跟单员手机号失败:', error);
            return null;
        }
    }

    /**
     * 获取订单信息
     * @param {string} orderNo 订单号
     * @param {string} factoryName 工厂名称
     * @returns {Promise<Object|null>} 订单信息
     */
    async getOrderInfo(orderNo, factoryName) {
        try {
            const sql = `
                SELECT order_no, receiver, follower, created_at, notice_date
                FROM shipping_detail
                WHERE order_no = ? AND receiver = ?
                LIMIT 1
            `;
            const result = await query(sql, [orderNo, factoryName]);
            return result.length > 0 ? result[0] : null;
        } catch (error) {
            console.error('❌ 获取订单信息失败:', error);
            return null;
        }
    }

    /**
     * 记录短信通知日志
     * @param {string} phoneNumber 手机号
     * @param {string} orderNo 订单号
     * @param {string} followerName 跟单员姓名
     * @param {string} notificationType 通知类型
     * @param {string} templateCode 模板CODE
     * @param {Object} templateParams 模板参数
     * @param {Object} result 发送结果
     */
    async logSMSNotification(phoneNumber, orderNo, followerName, notificationType, templateCode, templateParams, result) {
        try {
            const sql = `
                INSERT INTO sms_logs (phone_numbers, sign_name, template_code, template_params,
                                    send_status, response_code, response_message, message_id,
                                    request_id, out_id, order_no, follower_name, notification_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            await query(sql, [
                phoneNumber,
                this.templates[notificationType].signName,
                templateCode,
                JSON.stringify(templateParams),
                result.success ? 1 : 0,
                result.success ? 'OK' : (result.code || 'ERROR'),
                result.success ? result.message : result.error,
                result.messageId || null,
                result.requestId || null,
                result.outId || null,
                orderNo,
                followerName,
                notificationType
            ]);

            console.log(`📝 短信通知日志已记录: ${notificationType} - ${orderNo}`);
        } catch (error) {
            console.error('❌ 记录短信通知日志失败:', error.message);
        }
    }

    /**
     * 使用事务连接记录短信通知日志
     * @param {Object} connection 数据库连接
     * @param {string} phoneNumber 手机号
     * @param {string} orderNo 订单号
     * @param {string} followerName 跟单员姓名
     * @param {string} notificationType 通知类型
     * @param {string} templateCode 模板CODE
     * @param {Object} templateParams 模板参数
     * @param {Object} result 发送结果
     */
    async logSMSNotificationWithConnection(connection, phoneNumber, orderNo, followerName, notificationType, templateCode, templateParams, result) {
        try {
            const sql = `
                INSERT INTO sms_logs (phone_numbers, sign_name, template_code, template_params,
                                    send_status, response_code, response_message, message_id,
                                    request_id, out_id, order_no, follower_name, notification_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            await connection.execute(sql, [
                phoneNumber,
                this.templates[notificationType].signName,
                templateCode,
                JSON.stringify(templateParams),
                result.success ? 1 : 0,
                result.success ? 'OK' : (result.code || 'ERROR'),
                result.success ? result.message : result.error,
                result.messageId || null,
                result.requestId || null,
                result.outId || null,
                orderNo,
                followerName,
                notificationType
            ]);

            console.log(`📝 短信通知日志已记录(事务): ${notificationType} - ${orderNo}`);
        } catch (error) {
            console.error('❌ 使用事务记录短信通知日志失败:', error.message);
            throw error; // 重新抛出错误以触发事务回滚
        }
    }

    /**
     * 标记订单为已发货
     * @param {string} orderNo 订单号
     * @param {string} factoryName 工厂名称
     */
    async markOrderAsShipped(orderNo, factoryName) {
        try {
            const sql = `
                UPDATE delayed_shipping_tracking
                SET is_shipped = 1, shipped_at = NOW(), updated_at = NOW()
                WHERE order_no = ? AND factory_name = ?
            `;
            await query(sql, [orderNo, factoryName]);
            console.log(`📦 订单已标记为发货: ${orderNo}`);
        } catch (error) {
            console.error('❌ 标记订单发货状态失败:', error);
        }
    }

    /**
     * 使用事务连接标记订单为已发货
     * @param {Object} connection 数据库连接
     * @param {string} orderNo 订单号
     * @param {string} factoryName 工厂名称
     */
    async markOrderAsShippedWithConnection(connection, orderNo, factoryName) {
        try {
            const sql = `
                UPDATE delayed_shipping_tracking
                SET is_shipped = 1, shipped_at = NOW(), updated_at = NOW()
                WHERE order_no = ? AND factory_name = ?
            `;
            await connection.execute(sql, [orderNo, factoryName]);
            console.log(`📦 订单已标记为发货(事务): ${orderNo}`);
        } catch (error) {
            console.error('❌ 使用事务标记订单发货状态失败:', error);
            throw error; // 重新抛出错误以触发事务回滚
        }
    }



    /**
     * 格式化日期时间
     * @param {Date|string} dateTime 日期时间
     * @returns {string} 格式化后的时间字符串
     */
    formatDateTime(dateTime) {
        if (!dateTime) return '';
        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * 清理跟单员姓名，去除手机号等不符合阿里云模板规范的内容
     * @param {string} followerName 原始跟单员姓名
     * @returns {string} 清理后的跟单员姓名
     */
    cleanFollowerName(followerName) {
        if (!followerName || typeof followerName !== 'string') {
            return '跟单员';
        }

        // 去除手机号码（11位数字）
        let cleanName = followerName.replace(/\s*1[3-9]\d{9}\s*/g, '');

        // 去除其他可能的数字和特殊字符
        cleanName = cleanName.replace(/[0-9\-\(\)\[\]]/g, '');

        // 去除多余的空格
        cleanName = cleanName.trim().replace(/\s+/g, ' ');

        // 限制长度（阿里云模板变量通常有长度限制）
        if (cleanName.length > 10) {
            cleanName = cleanName.substring(0, 10);
        }

        // 如果清理后为空，使用默认值
        if (!cleanName || cleanName.length === 0) {
            cleanName = '跟单员';
        }

        console.log(`📝 跟单员姓名清理: "${followerName}" → "${cleanName}"`);
        return cleanName;
    }
}

module.exports = SMSNotificationService;
