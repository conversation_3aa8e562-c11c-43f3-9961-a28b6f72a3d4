# 用户身份识别功能（Position Field）

## 功能概述

本次更新为跟单管理系统添加了用户身份识别功能，通过`position`字段区分不同类型的用户，并根据用户身份显示不同的界面和数据。

## 用户身份类型

- **position = 1**: 跟单员
- **position = 2**: 订坯人员  
- **position = 3**: 跟单员和订坯员（双重身份，按订坯人员处理）

## 主要变更

### 1. 数据库变更

#### 修改 `follower_login` 表
- 添加 `position` 字段（tinyint，默认值为1）
- 添加 `idx_position` 索引
- 执行脚本：`server/scripts/add_position_field.sql`

### 2. 后端API变更

#### 跟单员登录API (`/api/auth/follower-login`)
- 返回数据中新增 `position` 字段
- 客户端可据此判断用户身份

#### 订单查询API
以下API根据用户身份返回不同数据源：

**跟单员 (position = 1)**
- 数据源：`shipping_detail` 表
- 查询条件：`sd.follower = followerName`

**订坯人员 (position = 2 或 3)**  
- 数据源：`caigou_order` 表
- 查询条件：`co.purchaser = followerName`

影响的API：
- `GET /api/orders/follower-history`
- `GET /api/orders/follower-query/:orderNumber`
- `POST /api/orders/follower-advanced-search`

### 3. 前端界面变更

#### 跟单管理页面 (`followerManage.vue`)

**动态标题和副标题**
- 跟单员：「跟单管理 - 跟单员工作台」
- 订坯人员：「订坯管理 - 订坯人员工作台」

**用户身份显示**
- position = 1：「跟单员」
- position = 2：「订坯人员」  
- position = 3：「跟单员&订坯员」

**字段标签适配**
- 跟单员：「坯布商」
- 订坯人员：「供应商」

**订坯人员专属字段**
- 显示「规格」信息（`order.spec`）

## 数据映射关系

### 跟单员数据 (shipping_detail)
```sql
SELECT 
    sd.order_no as order_number,
    sd.receiver as factory_name,
    sd.follower,
    sd.notice_date,
    sd.deliver_company,
    sd.product_name,
    sd.spec,
    sd.quantity,
    sd.created_at,
    co.supplier
FROM shipping_detail sd
LEFT JOIN caigou_order co ON sd.order_no = co.order_id
WHERE sd.follower = ?
```

### 订坯人员数据 (caigou_order)  
```sql
SELECT 
    co.order_id as order_number,
    co.supplier as factory_name,
    co.purchaser as follower,
    co.order_date as notice_date,
    co.supplier as deliver_company,
    co.product_name,
    co.material_spec as spec,
    co.greige_amount as quantity,
    co.created_at,
    co.supplier
FROM caigou_order co
WHERE co.purchaser = ?
```

## 部署步骤

### 1. 数据库更新
```sql
-- 执行SQL脚本
source server/scripts/add_position_field.sql;
```

### 2. 后端部署
```bash
# 重启后端服务
pm2 restart warehouse-api
```

### 3. 前端部署
```bash
# 重新构建前端
cd front
npm run build:mp-weixin
```

### 4. 用户数据配置
根据实际业务需求，更新用户的`position`值：
```sql
-- 设置某用户为订坯人员
UPDATE follower_login SET position = 2 WHERE username = 'username';

-- 设置某用户为双重身份
UPDATE follower_login SET position = 3 WHERE username = 'username';
```

## 测试验证

### 1. 登录测试
- 不同position用户登录后，检查返回的用户信息是否包含正确的position值

### 2. 界面测试  
- position=1：显示「跟单管理」界面，字段标签为「坯布商」
- position=2/3：显示「订坯管理」界面，字段标签为「供应商」，显示规格信息

### 3. 数据测试
- position=1：查询shipping_detail表数据
- position=2/3：查询caigou_order表数据
- 确认搜索、高级检索功能正常

## 注意事项

1. **向后兼容性**：现有用户的position默认为1（跟单员），保持原有功能不变
2. **数据一致性**：确保caigou_order表中的purchaser字段与follower_login表中的follower_name字段匹配
3. **权限控制**：不同身份用户只能查看自己负责的订单数据
4. **UI适配**：界面根据用户身份动态调整，提升用户体验

## 技术细节

- 使用Vue.js的计算属性和条件渲染实现界面动态切换
- 后端API采用统一的参数结构，根据position值选择不同的SQL查询
- 保持API接口向后兼容，现有客户端仍可正常使用 