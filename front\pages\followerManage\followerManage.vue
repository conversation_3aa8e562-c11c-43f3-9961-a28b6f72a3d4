<template>
	<view class="container">
		<!-- 顶部用户信息 -->
		<view class="header" v-if="currentUser">
			<view class="title-section">
				<text class="title">跟单管理</text>
				<text class="subtitle">跟单员工作台</text>
			</view>
			<view class="user-section">
				<view class="user-info">
					<text class="user-name">{{ currentUser.follower_name }}</text>
					<text class="user-dept">跟单员</text>
				</view>
				<button class="logout-btn" @click="handleLogout">
					<text class="logout-icon">🚪</text>
				</button>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 短信通知设置区域 -->
			<view class="notification-section">
				<view class="section-title">
					<text class="title-icon">📱</text>
					<text class="title-text">短信通知设置</text>
				</view>

				<view class="notification-card">
					<view class="card-header">
						<text class="card-title">短信通知开关</text>
						<switch
							:checked="notificationSettings.allowed"
							@change="onNotificationToggle"
							:disabled="!notificationSettings.hasPhone"
							color="#667eea"
						/>
					</view>

					<view class="card-content">
						<view class="phone-section">
							<view class="phone-label">
								<text class="label-text">手机号码</text>
								<text class="required-mark" v-if="!notificationSettings.hasPhone">*</text>
							</view>
							<view class="phone-input-group">
								<input
									class="phone-input"
									type="number"
									placeholder="请输入手机号码"
									v-model="phoneInput"
									:disabled="isUpdatingPhone"
								/>
								<button
									class="phone-btn"
									@click="updatePhone"
									:disabled="isUpdatingPhone"
								>
									{{ notificationSettings.hasPhone ? '修改' : '添加' }}
								</button>
							</view>
						</view>

						<view class="notification-tip" v-if="!notificationSettings.hasPhone">
							<text class="tip-icon">💡</text>
							<text class="tip-text">请先添加手机号码才能开启短信通知</text>
						</view>

						<view class="notification-status" v-else>
							<text class="status-icon">{{ notificationSettings.allowed ? '✅' : '❌' }}</text>
							<text class="status-text">
								短信通知已{{ notificationSettings.allowed ? '开启' : '关闭' }}
							</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 订单查询区域 -->
			<view class="search-section">
				<view class="section-title">
					<text class="title-icon">🔍</text>
					<text class="title-text">订单查询</text>
				</view>

				<view class="search-container">
					<!-- 基础搜索框 -->
					<view class="search-input-wrapper">
						<input
							class="search-input"
							type="text"
							placeholder="请输入订单号进行查询"
							v-model="searchKeyword"
							@input="onSearchInput"
							@confirm="handleSearch"
						/>
						<view class="search-btn" @click="handleSearch">
							<text class="search-icon">🔍</text>
						</view>
						<!-- 高级检索切换按钮 -->
						<view class="advanced-toggle-btn" @click="toggleAdvancedSearch">
							<text class="toggle-icon">{{ showAdvancedSearch ? '🔼' : '🔽' }}</text>
						</view>
					</view>

					<!-- 高级检索框 -->
					<view class="advanced-search" v-if="showAdvancedSearch">
						<view class="advanced-title">
							<text class="advanced-icon">⚙️</text>
							<text class="advanced-text">高级检索</text>
						</view>

						<!-- 坯布商检索 -->
						<view class="filter-item">
							<text class="filter-label">坯布商</text>
							<view class="filter-input-wrapper">
								<input
									class="filter-input"
									type="text"
									placeholder="请输入坯布商名称"
									v-model="advancedFilters.factoryName"
									@input="onAdvancedFilterChange"
								/>
								<view class="clear-btn" v-if="advancedFilters.factoryName" @click="clearFactoryFilter">
									<text class="clear-icon">✕</text>
								</view>
							</view>
						</view>

						<!-- 日期检索 -->
						<view class="filter-item">
							<text class="filter-label">日期范围</text>
							<view class="date-range-wrapper">
								<view class="date-input-wrapper">
									<picker
										mode="date"
										:value="advancedFilters.startDate"
										@change="onStartDateChange"
									>
										<view class="date-input">
											<text class="date-text">{{ advancedFilters.startDate || '开始日期' }}</text>
											<text class="date-icon">📅</text>
										</view>
									</picker>
								</view>
								<text class="date-separator">至</text>
								<view class="date-input-wrapper">
									<picker
										mode="date"
										:value="advancedFilters.endDate"
										@change="onEndDateChange"
									>
										<view class="date-input">
											<text class="date-text">{{ advancedFilters.endDate || '结束日期' }}</text>
											<text class="date-icon">📅</text>
										</view>
									</picker>
								</view>
							</view>
						</view>

						<!-- 高级检索操作按钮 -->
						<view class="advanced-actions">
							<button class="action-btn reset-btn" @click="resetAdvancedFilters">
								<text class="btn-icon">🔄</text>
								<text class="btn-text">重置</text>
							</button>
							<button class="action-btn clear-btn" @click="clearSearchResults">
								<text class="btn-icon">🗑️</text>
								<text class="btn-text">清空</text>
							</button>
							<button class="action-btn search-btn-advanced" @click="handleAdvancedSearch">
								<text class="btn-icon">🔍</text>
								<text class="btn-text">检索</text>
							</button>
						</view>
					</view>
				</view>

				<!-- 搜索结果 -->
				<view class="search-results" v-if="searchResults.length > 0">
					<view class="results-header">
						<text class="results-title">搜索结果 ({{ searchResults.length }})</text>
					</view>
					<view
						class="order-item"
						v-for="(order, index) in searchResults"
						:key="index"
						@click="viewOrderDetail(order)"
					>
						<view class="order-header">
							<view class="order-number">{{ order.order_number }}</view>
							<view class="order-status-container">
								<view class="image-count">{{ order.image_count }} 张图片</view>
								<view
									class="shipping-status"
									:class="getShippingStatusClass(order.shipping_status)"
								>
									{{ order.shipping_status_text || '未知' }}
									<text v-if="order.shipping_status === 'overdue' && order.delay_days" class="delay-days">
										({{ order.delay_days }}天)
									</text>
								</view>
							</view>
						</view>
						<view class="order-info">
							<text class="info-item">坯布商: {{ order.supplier || order.factory_name || '未指定' }}</text>
							<text class="info-item">品名: {{ order.product_name || '未指定' }}</text>
						</view>
						<view class="order-footer">
							<text class="order-date">{{ formatDate(order.created_at) }}</text>
							<text class="order-company">{{ order.deliver_company || '未指定' }}</text>
						</view>
					</view>
				</view>

				<!-- 搜索无结果 -->
				<view class="no-results" v-if="showNoResults">
					<text class="no-results-icon">📭</text>
					<text class="no-results-text">未找到匹配的订单</text>
					<text class="no-results-desc">请检查订单号是否正确</text>
				</view>
			</view>

			<!-- 历史订单区域 -->
			<view class="history-section">
				<view class="section-title">
					<text class="title-icon">📋</text>
					<text class="title-text">最新订单 ({{ orderHistory.length }})</text>
				</view>

				<view class="order-list" v-if="orderHistory.length > 0">
					<view
						class="order-item"
						v-for="(order, index) in orderHistory"
						:key="index"
						@click="viewOrderDetail(order)"
					>
						<view class="order-header">
							<view class="order-number">{{ order.order_number }}</view>
							<view class="order-status-container">
								<view class="image-count">{{ order.image_count }} 张图片</view>
								<view
									class="shipping-status"
									:class="getShippingStatusClass(order.shipping_status)"
								>
									{{ order.shipping_status_text || '未知' }}
									<text v-if="order.shipping_status === 'overdue' && order.delay_days" class="delay-days">
										({{ order.delay_days }}天)
									</text>
								</view>
							</view>
						</view>
						<view class="order-info">
							<text class="info-item">坯布商: {{ order.supplier || order.factory_name || '未指定' }}</text>
							<text class="info-item">品名: {{ order.product_name || '未指定' }}</text>
						</view>
						<view class="order-footer">
							<text class="order-date">{{ formatDate(order.created_at) }}</text>
							<text class="order-company">{{ order.deliver_company || '未指定' }}</text>
						</view>
					</view>
				</view>

				<!-- 暂无订单 -->
				<view class="empty-state" v-if="orderHistory.length === 0 && !isLoading">
					<text class="empty-icon">📦</text>
					<text class="empty-text">暂无订单记录</text>
					<text class="empty-desc">您负责的订单将在这里显示</text>
				</view>

				<!-- 加载状态 -->
				<view class="loading-state" v-if="isLoading">
					<text class="loading-icon">⏳</text>
					<text class="loading-text">正在加载订单数据...</text>
				</view>
			</view>
		</view>

		<!-- 底部信息 -->
		<view class="footer">
			<text class="footer-text">跟单管理系统 v1.0.0</text>
		</view>


	</view>
</template>

<script>
	import followerUserManager from '@/utils/followerUserManager.js';
	import warehouseUserManager from '@/utils/warehouseUsers.js';
	import urlConfig from '@/utils/urlConfig.js';
	import { showSuccess, showError } from '@/utils/helpers.js';
	import apiService from '@/utils/apiService.js';

	export default {
		data() {
			return {
				currentUser: null,
				searchKeyword: '',
				searchResults: [],
				orderHistory: [],
				isLoading: false,
				showNoResults: false,
				searchTimeout: null,
				// 高级检索相关
				showAdvancedSearch: false,
				advancedFilters: {
					factoryName: '',
					startDate: '',
					endDate: ''
				},
				// 短信通知设置相关
				notificationSettings: {
					allowed: false,
					phone: '',
					hasPhone: false
				},
				phoneInput: '',
				isUpdatingPhone: false
			}
		},
		methods: {


			/**
			 * 处理搜索输入
			 */
			onSearchInput() {
				// 清除之前的搜索结果
				this.searchResults = [];
				this.showNoResults = false;

				// 防抖处理
				if (this.searchTimeout) {
					clearTimeout(this.searchTimeout);
				}

				if (this.searchKeyword.trim()) {
					this.searchTimeout = setTimeout(() => {
						this.handleSearch();
					}, 500);
				}
			},

			/**
			 * 处理搜索
			 */
			async handleSearch() {
				if (!this.searchKeyword.trim()) {
					this.searchResults = [];
					this.showNoResults = false;
					return;
				}

				try {
					console.log('🔍 搜索订单:', this.searchKeyword);

					const token = followerUserManager.getToken();
					if (!token) {
						showError('请先登录');
						return;
					}

					const response = await apiService.request({
						url: `/api/orders/follower-query/${encodeURIComponent(this.searchKeyword.trim())}`,
						method: 'GET',
						header: {
							'Authorization': `Bearer ${token}`
						}
					});

					if (response.success) {
						// 处理搜索结果
						if (Array.isArray(response.data)) {
							this.searchResults = response.data;
						} else {
							this.searchResults = [response.data];
						}
						this.showNoResults = false;
						console.log('✅ 搜索成功，找到', this.searchResults.length, '个订单');
					} else {
						this.searchResults = [];
						this.showNoResults = true;
						console.log('⚠️ 搜索无结果:', response.message);
					}
				} catch (error) {
					console.error('❌ 搜索订单失败:', error);
					this.searchResults = [];
					this.showNoResults = true;
					showError('搜索失败，请稍后重试');
				}
			},

			/**
			 * 查看订单详情
			 */
			async viewOrderDetail(order) {
				console.log('📋 查看订单详情:', order.order_number);

				try {
					// 显示加载提示
					uni.showLoading({
						title: '获取访问权限...'
					});

					// 保存当前跟单员用户信息
					const currentFollowerInfo = followerUserManager.getUserInfo();
					uni.setStorageSync('follower_user_info', currentFollowerInfo);

					// 获取工厂访问token
					const accessToken = await this.getFactoryAccessToken(order.factory_name);

					if (accessToken) {
						console.log('✅ 获取到工厂访问token:', accessToken);
						// 将访问token存储到临时存储中
						uni.setStorageSync('temp_factory_access_token', accessToken);

						// 清除仓库管理登录状态，确保跟单员访问结束后需要重新登录仓库管理
						try {
							warehouseUserManager.logout();
							console.log('✅ 跟单员访问开始，已清除仓库管理登录状态');
						} catch (error) {
							console.error('❌ 清除仓库管理登录状态失败:', error);
						}

						// 跳转到图片管理页面
						uni.navigateTo({
							url: `/pages/imageManage/imageManage?orderNumber=${order.order_number}&factoryName=${order.factory_name}&readonly=true&followerAccess=true`
						});
					}
				} catch (error) {
					console.error('❌ 获取工厂访问权限失败:', error);
					showError(error.message || '获取访问权限失败');
				} finally {
					uni.hideLoading();
				}
			},

			/**
			 * 获取工厂访问token
			 */
			async getFactoryAccessToken(factoryName) {
				return new Promise((resolve, reject) => {
					const requestConfig = followerUserManager.createAuthRequest({
						url: urlConfig.getApiUrl('/api/auth/follower-factory-access'),
						method: 'POST',
						data: {
							factory_name: factoryName
						},
						timeout: 10000,
						success: (res) => {
							if (res.statusCode === 200 && res.data.success) {
								resolve(res.data.data);
							} else {
								reject(new Error(res.data?.message || '获取访问权限失败'));
							}
						},
						fail: (err) => {
							console.error('获取工厂访问token请求失败:', err);
							reject(new Error('网络连接失败'));
						}
					});

					uni.request(requestConfig);
				});
			},

			/**
			 * 格式化日期
			 */
			formatDate(dateString) {
				if (!dateString) return '未知时间';

				try {
					const date = new Date(dateString);
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					const hours = String(date.getHours()).padStart(2, '0');
					const minutes = String(date.getMinutes()).padStart(2, '0');

					return `${year}-${month}-${day} ${hours}:${minutes}`;
				} catch (error) {
					return '时间格式错误';
				}
			},

			/**
			 * 加载订单历史
			 */
			async loadOrderHistory() {
				try {
					this.isLoading = true;
					console.log('📋 加载跟单员订单历史...');

					const token = followerUserManager.getToken();
					if (!token) {
						showError('请先登录');
						return;
					}

					const response = await apiService.request({
						url: '/api/orders/follower-history',
						method: 'GET',
						header: {
							'Authorization': `Bearer ${token}`
						}
					});

					if (response.success) {
						this.orderHistory = response.data || [];
						console.log('✅ 订单历史加载成功，共', this.orderHistory.length, '个订单');
					} else {
						console.error('❌ 加载订单历史失败:', response.message);
						showError('加载订单历史失败');
					}
				} catch (error) {
					console.error('❌ 加载订单历史异常:', error);
					showError('加载订单历史失败，请稍后重试');
				} finally {
					this.isLoading = false;
				}
			},

			/**
			 * 处理登出
			 */
			handleLogout() {
				uni.showModal({
					title: '确认登出',
					content: '您确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							// 执行登出
							const logoutSuccess = followerUserManager.logout();

							if (logoutSuccess) {
								showSuccess('已退出登录');

								// 跳转到首页
								setTimeout(() => {
									uni.reLaunch({
										url: '/pages/home/<USER>'
									});
								}, 1500);
							} else {
								showError('退出登录失败');
							}
						}
					}
				});
			},

			/**
			 * 初始化用户信息
			 */
			initUserInfo() {
				// 获取当前登录的跟单员信息
				this.currentUser = followerUserManager.getUserInfo();

				if (!this.currentUser) {
					// 未登录，跳转到登录页面
					uni.reLaunch({
						url: '/pages/followerLogin/followerLogin'
					});
					return;
				}

				console.log('✅ 跟单员信息加载成功:', this.currentUser);

				// 加载订单历史
				this.loadOrderHistory();

				// 加载短信通知设置
				this.loadNotificationSettings();
			},

			/**
			 * 加载短信通知设置
			 */
			async loadNotificationSettings() {
				try {
					console.log('📱 加载短信通知设置...');

					const token = followerUserManager.getToken();
					if (!token) {
						console.error('❌ 未找到token');
						return;
					}

					const response = await apiService.request({
						url: '/api/sms/notification-settings',
						method: 'GET',
						header: {
							'Authorization': `Bearer ${token}`
						}
					});

					if (response.success) {
						this.notificationSettings = response.data;
						this.phoneInput = response.data.phone || '';
						console.log('✅ 短信通知设置加载成功:', response.data);
					} else {
						console.error('❌ 加载短信通知设置失败:', response.message);
						showError('加载短信通知设置失败');
					}
				} catch (error) {
					console.error('❌ 加载短信通知设置异常:', error);
					showError('加载短信通知设置失败，请稍后重试');
				}
			},

			/**
			 * 短信通知开关切换
			 */
			async onNotificationToggle(event) {
				const newValue = event.detail.value;

				if (!this.notificationSettings.hasPhone) {
					showError('请先添加手机号码');
					return;
				}

				try {
					console.log('📱 切换短信通知开关:', newValue);

					const token = followerUserManager.getToken();
					if (!token) {
						showError('请先登录');
						return;
					}

					const response = await apiService.request({
						url: '/api/sms/notification-settings',
						method: 'PUT',
						data: {
							allowed: newValue
						},
						header: {
							'Authorization': `Bearer ${token}`
						}
					});

					if (response.success) {
						this.notificationSettings.allowed = newValue;
						showSuccess(newValue ? '短信通知已开启' : '短信通知已关闭');
						console.log('✅ 短信通知开关更新成功');
					} else {
						console.error('❌ 更新短信通知开关失败:', response.message);
						showError('更新失败：' + response.message);
					}
				} catch (error) {
					console.error('❌ 更新短信通知开关异常:', error);
					showError('更新失败，请稍后重试');
				}
			},

			/**
			 * 更新手机号码
			 */
			async updatePhone() {
				const phone = this.phoneInput.trim();

				if (!phone) {
					showError('请输入手机号码');
					return;
				}

				// 验证手机号格式
				const phoneRegex = /^1[3-9]\d{9}$/;
				if (!phoneRegex.test(phone)) {
					showError('手机号格式不正确');
					return;
				}

				try {
					this.isUpdatingPhone = true;
					console.log('📱 更新手机号码:', phone);

					const token = followerUserManager.getToken();
					if (!token) {
						showError('请先登录');
						return;
					}

					const response = await apiService.request({
						url: '/api/sms/notification-settings',
						method: 'PUT',
						data: {
							phone: phone
						},
						header: {
							'Authorization': `Bearer ${token}`
						}
					});

					if (response.success) {
						this.notificationSettings.phone = phone;
						this.notificationSettings.hasPhone = true;
						showSuccess('手机号码更新成功');
						console.log('✅ 手机号码更新成功');
					} else {
						console.error('❌ 更新手机号码失败:', response.message);
						showError('更新失败：' + response.message);
					}
				} catch (error) {
					console.error('❌ 更新手机号码异常:', error);
					showError('更新失败，请稍后重试');
				} finally {
					this.isUpdatingPhone = false;
				}
			},

			/**
			 * 切换高级检索显示状态
			 */
			toggleAdvancedSearch() {
				this.showAdvancedSearch = !this.showAdvancedSearch;
				console.log('🔽 切换高级检索:', this.showAdvancedSearch);
			},

			/**
			 * 高级筛选条件变化
			 */
			onAdvancedFilterChange() {
				// 可以在这里添加实时筛选逻辑
				console.log('🔍 高级筛选条件变化:', this.advancedFilters);
			},

			/**
			 * 清除坯布商筛选
			 */
			clearFactoryFilter() {
				this.advancedFilters.factoryName = '';
				this.onAdvancedFilterChange();
			},

			/**
			 * 开始日期变化
			 */
			onStartDateChange(e) {
				this.advancedFilters.startDate = e.detail.value;
				console.log('📅 开始日期变化:', this.advancedFilters.startDate);
				this.onAdvancedFilterChange();
			},

			/**
			 * 结束日期变化
			 */
			onEndDateChange(e) {
				this.advancedFilters.endDate = e.detail.value;
				console.log('📅 结束日期变化:', this.advancedFilters.endDate);
				this.onAdvancedFilterChange();
			},

			/**
			 * 重置高级筛选条件
			 */
			resetAdvancedFilters() {
				this.advancedFilters = {
					factoryName: '',
					startDate: '',
					endDate: ''
				};
				console.log('🔄 重置高级筛选条件');
				// 重置后可以选择是否立即执行搜索
				this.handleAdvancedSearch();
			},

			/**
			 * 清空检索结果
			 */
			clearSearchResults() {
				this.searchResults = [];
				this.showNoResults = false;
				this.searchKeyword = '';
				this.advancedFilters = {
					factoryName: '',
					startDate: '',
					endDate: ''
				};
				console.log('🗑️ 清空检索结果');
				showSuccess('已清空检索结果');
			},

			/**
			 * 执行高级检索
			 */
			async handleAdvancedSearch() {
				console.log('🔍 执行高级检索:', this.advancedFilters);

				try {
					this.isLoading = true;
					this.searchResults = [];
					this.showNoResults = false;

					// 构建查询参数
					const queryParams = {};

					// 添加基础搜索关键词
					if (this.searchKeyword.trim()) {
						queryParams.orderNumber = this.searchKeyword.trim();
					}

					// 添加坯布商筛选
					if (this.advancedFilters.factoryName.trim()) {
						queryParams.factoryName = this.advancedFilters.factoryName.trim();
					}

					// 添加日期范围筛选
					if (this.advancedFilters.startDate) {
						queryParams.startDate = this.advancedFilters.startDate;
					}
					if (this.advancedFilters.endDate) {
						queryParams.endDate = this.advancedFilters.endDate;
					}

					console.log('📋 高级检索参数:', queryParams);

					// 调用后端API进行高级检索
					const response = await this.advancedSearchRequest(queryParams);

					if (response.success) {
						this.searchResults = response.data || [];
						this.showNoResults = this.searchResults.length === 0;

						if (this.searchResults.length > 0) {
							showSuccess(`找到 ${this.searchResults.length} 条相关订单`);
						} else {
							showError('未找到符合条件的订单');
						}
					} else {
						throw new Error(response.message || '检索失败');
					}
				} catch (error) {
					console.error('❌ 高级检索失败:', error);
					this.searchResults = [];
					this.showNoResults = true;
					showError('检索失败，请稍后重试');
				} finally {
					this.isLoading = false;
				}
			},

			/**
			 * 高级检索请求
			 */
			async advancedSearchRequest(queryParams) {
				return new Promise((resolve, reject) => {
					const requestConfig = followerUserManager.createAuthRequest({
						url: urlConfig.getApiUrl('/api/orders/follower-advanced-search'),
						method: 'POST',
						data: queryParams,
						timeout: 10000,
						success: (res) => {
							if (res.statusCode === 200) {
								resolve(res.data);
							} else {
								reject(new Error(`服务器错误 (${res.statusCode})`));
							}
						},
						fail: (err) => {
							console.error('高级检索请求失败:', err);
							reject(new Error('网络连接失败'));
						}
					});

					uni.request(requestConfig);
				});
			},

			/**
			 * 获取发货状态样式类名
			 */
			getShippingStatusClass(status) {
				switch (status) {
					case 'shipped':
						return 'status-shipped';
					case 'overdue':
						return 'status-overdue';
					case 'pending':
						return 'status-pending';
					default:
						return 'status-unknown';
				}
			}
		},

		onLoad() {
			// 检查登录状态
			if (!followerUserManager.checkLoginStatus()) {
				// 未登录，跳转到登录页面
				uni.reLaunch({
					url: '/pages/followerLogin/followerLogin'
				});
				return;
			}

			// 初始化用户信息
			this.initUserInfo();
		},

		onShow() {
			// 页面显示时刷新用户信息
			if (followerUserManager.checkLoginStatus()) {
				this.initUserInfo();
			}
		}
	}
</script>

<style scoped>
	.container {
		min-height: 100vh;
		background: #f5f7fa;
		display: flex;
		flex-direction: column;
	}

	/* 头部样式 */
	.header {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 40rpx 30rpx 30rpx;
		color: white;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
	}

	.title-section {
		flex: 1;
	}

	.title {
		font-size: 44rpx;
		font-weight: bold;
		margin-bottom: 8rpx;
		display: block;
	}

	.subtitle {
		font-size: 26rpx;
		opacity: 0.9;
		display: block;
	}

	.user-section {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.user-info {
		text-align: right;
	}

	.user-name {
		font-size: 32rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 4rpx;
	}

	.user-dept {
		font-size: 24rpx;
		opacity: 0.8;
		display: block;
	}

	.logout-btn {
		width: 70rpx;
		height: 70rpx;
		background: rgba(255, 255, 255, 0.2);
		border: none;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		backdrop-filter: blur(10rpx);
		transition: all 0.3s ease;
	}

	.logout-btn:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}

	.logout-icon {
		font-size: 32rpx;
		color: white;
	}

	/* 主要内容区域 */
	.main-content {
		flex: 1;
		padding: 0;
	}

	/* 短信通知设置区域 */
	.notification-section {
		background: white;
		border-radius: 20rpx;
		padding: 40rpx;
		margin: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid #f0f0f0;
	}

	.notification-card {
		margin-top: 20rpx;
	}

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #f0f0f0;
	}

	.card-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.card-content {
		padding-top: 20rpx;
	}

	.phone-section {
		margin-bottom: 20rpx;
	}

	.phone-label {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx;
	}

	.label-text {
		font-size: 28rpx;
		color: #666;
	}

	.required-mark {
		color: #ff4757;
		margin-left: 5rpx;
		font-size: 28rpx;
	}

	.phone-input-group {
		display: flex;
		gap: 20rpx;
		align-items: center;
	}

	.phone-input {
		flex: 1;
		height: 80rpx;
		padding: 0 20rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 10rpx;
		font-size: 28rpx;
		background: #fff;
	}

	.phone-input:focus {
		border-color: #667eea;
	}

	.phone-btn {
		height: 80rpx;
		padding: 0 30rpx;
		background: #667eea;
		color: white;
		border: none;
		border-radius: 10rpx;
		font-size: 28rpx;
		font-weight: bold;
	}

	.phone-btn:disabled {
		background: #ccc;
	}

	.notification-tip {
		display: flex;
		align-items: center;
		gap: 10rpx;
		padding: 15rpx 20rpx;
		background: #fff3cd;
		border: 2rpx solid #ffeaa7;
		border-radius: 10rpx;
		margin-top: 20rpx;
	}

	.tip-icon {
		font-size: 28rpx;
	}

	.tip-text {
		font-size: 26rpx;
		color: #856404;
	}

	.notification-status {
		display: flex;
		align-items: center;
		gap: 10rpx;
		padding: 15rpx 20rpx;
		background: #f8f9fa;
		border-radius: 10rpx;
		margin-top: 20rpx;
	}

	.status-icon {
		font-size: 28rpx;
	}

	.status-text {
		font-size: 26rpx;
		color: #666;
	}

	/* 搜索区域样式 */
	.search-section {
		background: white;
		border-radius: 20rpx;
		padding: 40rpx;
		margin: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid #f0f0f0;
	}

	/* 历史订单区域样式 */
	.history-section {
		margin: 0 30rpx;
	}

	.section-title {
		display: flex;
		align-items: center;
		gap: 15rpx;
		margin-bottom: 25rpx;
		padding: 0 10rpx;
	}

	.title-icon {
		font-size: 40rpx;
	}

	.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.search-container {
		margin-bottom: 30rpx;
	}

	.search-input-wrapper {
		display: flex;
		align-items: center;
		background: #f8f9fa;
		border-radius: 50rpx;
		padding: 0 20rpx;
		border: 2rpx solid #e9ecef;
		transition: all 0.3s ease;
	}

	.search-input-wrapper:focus-within {
		border-color: #667eea;
		background: white;
		box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
	}

	.search-input {
		flex: 1;
		height: 80rpx;
		font-size: 28rpx;
		color: #333;
		background: transparent;
		border: none;
		outline: none;
	}

	.search-btn {
		width: 60rpx;
		height: 60rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.search-btn:active {
		transform: scale(0.95);
	}

	.search-icon {
		font-size: 28rpx;
		color: white;
	}

	/* 高级检索切换按钮 */
	.advanced-toggle-btn {
		width: 60rpx;
		height: 60rpx;
		background: #6c757d;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 10rpx;
		transition: all 0.3s ease;
	}

	.advanced-toggle-btn:active {
		transform: scale(0.95);
		background: #5a6268;
	}

	.toggle-icon {
		font-size: 24rpx;
		color: white;
	}

	/* 高级检索框 */
	.advanced-search {
		margin-top: 30rpx;
		padding: 30rpx;
		background: #f8f9fa;
		border-radius: 16rpx;
		border: 1rpx solid #e9ecef;
		animation: slideDown 0.3s ease;
	}

	@keyframes slideDown {
		from {
			opacity: 0;
			transform: translateY(-20rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.advanced-title {
		display: flex;
		align-items: center;
		gap: 10rpx;
		margin-bottom: 30rpx;
		padding-bottom: 15rpx;
		border-bottom: 1rpx solid #dee2e6;
	}

	.advanced-icon {
		font-size: 28rpx;
		color: #667eea;
	}

	.advanced-text {
		font-size: 28rpx;
		font-weight: bold;
		color: #495057;
	}

	/* 筛选项 */
	.filter-item {
		margin-bottom: 25rpx;
	}

	.filter-label {
		display: block;
		font-size: 26rpx;
		color: #495057;
		margin-bottom: 10rpx;
		font-weight: 500;
	}

	.filter-input-wrapper {
		display: flex;
		align-items: center;
		background: white;
		border-radius: 12rpx;
		border: 1rpx solid #ced4da;
		padding: 0 20rpx;
		transition: all 0.3s ease;
	}

	.filter-input-wrapper:focus-within {
		border-color: #667eea;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}

	.filter-input {
		flex: 1;
		height: 70rpx;
		font-size: 26rpx;
		color: #495057;
		background: transparent;
		border: none;
		outline: none;
	}

	.clear-btn {
		width: 40rpx;
		height: 40rpx;
		background: #dc3545;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 10rpx;
		transition: all 0.3s ease;
	}

	.clear-btn:active {
		transform: scale(0.9);
		background: #c82333;
	}

	.clear-icon {
		font-size: 20rpx;
		color: white;
	}

	/* 日期范围 */
	.date-range-wrapper {
		display: flex;
		align-items: center;
		gap: 15rpx;
	}

	.date-input-wrapper {
		flex: 1;
	}

	.date-input {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: white;
		border-radius: 12rpx;
		border: 1rpx solid #ced4da;
		padding: 20rpx;
		height: 70rpx;
		transition: all 0.3s ease;
	}

	.date-input:active {
		border-color: #667eea;
		background: #f8f9ff;
	}

	.date-text {
		font-size: 26rpx;
		color: #495057;
	}

	.date-icon {
		font-size: 24rpx;
		color: #6c757d;
	}

	.date-separator {
		font-size: 24rpx;
		color: #6c757d;
		font-weight: 500;
	}

	/* 高级检索操作按钮 */
	.advanced-actions {
		display: flex;
		gap: 15rpx;
		margin-top: 30rpx;
		padding-top: 20rpx;
		border-top: 1rpx solid #dee2e6;
	}

	.action-btn {
		flex: 1;
		height: 70rpx;
		border-radius: 12rpx;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 6rpx;
		font-size: 24rpx;
		font-weight: 500;
		transition: all 0.3s ease;
	}

	.reset-btn {
		background: #6c757d;
		color: white;
	}

	.reset-btn:active {
		background: #5a6268;
		transform: scale(0.98);
	}

	.clear-btn {
		background: #dc3545;
		color: white;
	}

	.clear-btn:active {
		background: #c82333;
		transform: scale(0.98);
	}

	.search-btn-advanced {
		background: #667eea;
		color: white;
	}

	.search-btn-advanced:active {
		background: #5a67d8;
		transform: scale(0.98);
	}

	.btn-icon {
		font-size: 24rpx;
	}

	.btn-text {
		font-size: 26rpx;
	}

	/* 欢迎卡片 */
	.welcome-card {
		background: white;
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		display: flex;
		align-items: center;
		gap: 30rpx;
	}

	.welcome-icon {
		font-size: 80rpx;
	}

	.welcome-text {
		flex: 1;
	}

	.welcome-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 10rpx;
	}

	.welcome-desc {
		font-size: 28rpx;
		color: #666;
		display: block;
	}

	/* 功能区域 */
	.function-area {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.function-card {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
		display: flex;
		align-items: center;
		gap: 25rpx;
		transition: all 0.3s ease;
	}

	.function-card:active {
		transform: scale(0.98);
		box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
	}

	.card-icon {
		font-size: 60rpx;
		width: 80rpx;
		text-align: center;
	}

	.card-content {
		flex: 1;
	}

	.card-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8rpx;
	}

	.card-desc {
		font-size: 26rpx;
		color: #666;
		display: block;
	}

	.card-status {
		font-size: 24rpx;
		color: #999;
		background: #f0f0f0;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
	}

	/* 订单列表样式 */
	.order-list {
		padding: 0;
	}

	.order-item {
		background: white;
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;
		border: 1rpx solid #f0f0f0;
	}

	.order-item:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
	}

	.order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 25rpx;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #f8f9fa;
	}

	.order-number {
		font-size: 36rpx;
		font-weight: bold;
		color: #2c3e50;
		letter-spacing: 1rpx;
	}

	.order-status-container {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		gap: 8rpx;
	}

	.image-count {
		font-size: 24rpx;
		color: #666;
		background: #e8f4fd;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		border: 1rpx solid #d1ecf1;
	}

	.shipping-status {
		font-size: 22rpx;
		font-weight: bold;
		padding: 6rpx 12rpx;
		border-radius: 16rpx;
		text-align: center;
		min-width: 80rpx;
	}

	.status-shipped {
		background: #d4edda;
		color: #155724;
		border: 1rpx solid #c3e6cb;
	}

	.status-pending {
		background: #fff3cd;
		color: #856404;
		border: 1rpx solid #ffeaa7;
	}

	.status-overdue {
		background: #f8d7da;
		color: #721c24;
		border: 1rpx solid #f5c6cb;
	}

	.status-unknown {
		background: #e2e3e5;
		color: #383d41;
		border: 1rpx solid #d6d8db;
	}

	.delay-days {
		font-size: 20rpx;
		margin-left: 4rpx;
	}

	.order-info {
		margin-bottom: 20rpx;
	}

	.order-info .info-item {
		display: block;
		font-size: 30rpx;
		color: #555;
		line-height: 2.2;
		margin-bottom: 8rpx;
		padding-left: 20rpx;
		position: relative;
	}

	.order-info .info-item:before {
		content: "•";
		position: absolute;
		left: 0;
		color: #667eea;
		font-weight: bold;
	}

	.order-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 25rpx;
		padding-top: 20rpx;
		border-top: 2rpx solid #f8f9fa;
	}

	.order-date {
		font-size: 28rpx;
		color: #7f8c8d;
		font-weight: 500;
	}

	.order-company {
		font-size: 28rpx;
		color: #667eea;
		font-weight: 500;
		background: #f8f9ff;
		padding: 8rpx 16rpx;
		border-radius: 15rpx;
	}

	/* 搜索结果样式 */
	.results-header {
		padding: 20rpx 30rpx 10rpx;
	}

	.results-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
	}

	/* 底部样式 */
	.footer {
		padding: 30rpx;
		text-align: center;
		background: white;
		border-top: 1rpx solid #eee;
	}

	.footer-text {
		font-size: 24rpx;
		color: #999;
	}


</style>
