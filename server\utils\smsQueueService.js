/**
 * 短信队列处理服务
 * 处理短信通知队列中的任务
 */

const SMSNotificationService = require('./smsNotificationService');
const DuplicateCheckService = require('./duplicateCheckService');
const { query, transaction } = require('../config/database');

class SMSQueueService {
    constructor(smsNotificationService = null) {
        // 支持依赖注入，避免重复创建实例
        this.smsNotificationService = smsNotificationService || new SMSNotificationService();
        this.duplicateCheckService = new DuplicateCheckService();
        this.isProcessing = false;
        this.maxRetryCount = 3;
        this.processingInterval = 5000; // 5秒检查一次

        // 只在没有注入依赖时输出日志
        if (!smsNotificationService) {
            console.log('📋 短信队列服务初始化完成');
        }
    }

    /**
     * 启动队列处理
     */
    startProcessing() {
        if (this.isProcessing) {
            console.log('⚠️ 短信队列处理已在运行中');
            return;
        }

        this.isProcessing = true;
        console.log('🚀 启动短信队列处理服务');

        // 立即处理一次
        this.processQueue();

        // 设置定时处理
        this.processingTimer = setInterval(() => {
            this.processQueue();
        }, this.processingInterval);
    }

    /**
     * 停止队列处理
     */
    stopProcessing() {
        if (!this.isProcessing) {
            console.log('⚠️ 短信队列处理未在运行');
            return;
        }

        this.isProcessing = false;
        if (this.processingTimer) {
            clearInterval(this.processingTimer);
            this.processingTimer = null;
        }
        console.log('⏹️ 短信队列处理服务已停止');
    }

    /**
     * 处理队列中的任务
     */
    async processQueue() {
        try {
            // 获取待处理的任务
            const pendingTasks = await this.getPendingTasks();
            
            if (pendingTasks.length === 0) {
                return; // 没有待处理任务
            }

            if (pendingTasks.length > 0) {
                console.log(`📋 处理 ${pendingTasks.length} 个短信任务`);
            }

            for (const task of pendingTasks) {
                await this.processTask(task);
                // 避免发送过快，间隔200ms
                await this.sleep(200);
            }

        } catch (error) {
            console.error('❌ 处理短信队列失败:', error);
        }
    }

    /**
     * 获取待处理的任务
     * @returns {Promise<Array>} 待处理任务列表
     */
    async getPendingTasks() {
        try {
            const sql = `
                SELECT id, order_no, factory_name, follower_name, phone_number, 
                       notification_type, template_code, template_params, retry_count
                FROM sms_notification_queue
                WHERE status = 'pending' 
                AND retry_count < ?
                AND (scheduled_time IS NULL OR scheduled_time <= NOW())
                ORDER BY created_at ASC
                LIMIT 10
            `;
            
            return await query(sql, [this.maxRetryCount]);
        } catch (error) {
            console.error('❌ 获取待处理任务失败:', error);
            return [];
        }
    }

    /**
     * 处理单个任务（带事务保护）
     * @param {Object} task 任务对象
     */
    async processTask(task) {
        try {
            console.log(`📤 处理短信任务: ${task.notification_type} - ${task.order_no}`);

            // 1. 更新任务状态为处理中
            await this.updateTaskStatus(task.id, 'processing');

            // 2. 发送短信通知（外部API调用，不需要在事务中）
            let smsResult;
            let templateParams;

            // 安全解析template_params，支持多种格式
            try {
                if (typeof task.template_params === 'string') {
                    templateParams = JSON.parse(task.template_params || '{}');
                } else if (typeof task.template_params === 'object' && task.template_params !== null) {
                    templateParams = task.template_params;
                } else {
                    templateParams = {};
                }
            } catch (error) {
                console.error(`❌ 解析template_params失败: ${task.template_params}`, error);
                templateParams = {};
            }

            // 根据通知类型调用相应的发送方法
            switch (task.notification_type) {
                case 'order_created':
                    smsResult = await this.smsNotificationService.sendOrderCreatedNotification(
                        task.order_no, task.factory_name, task.follower_name
                    );
                    break;

                case 'order_shipped':
                    smsResult = await this.smsNotificationService.sendOrderShippedNotification(
                        task.order_no, task.factory_name, task.follower_name
                    );
                    break;

                case 'delayed_shipping':
                    const delayDays = templateParams.delayDays || 3;
                    smsResult = await this.smsNotificationService.sendDelayedShippingNotification(
                        task.order_no, task.factory_name, task.follower_name, parseInt(delayDays)
                    );
                    break;

                default:
                    throw new Error(`不支持的通知类型: ${task.notification_type}`);
            }

            // 3. 使用事务保护状态更新和日志记录
            const result = await transaction(async (connection) => {
                if (smsResult.success) {
                    // 发送成功，更新任务状态
                    await this.updateTaskStatusWithConnection(connection, task.id, 'completed', null, smsResult.messageId);
                    return { success: true, result: smsResult };
                } else {
                    // 发送失败，增加重试次数
                    await this.handleTaskFailureWithConnection(connection, task, smsResult.error || smsResult.message);
                    return { success: false, result: smsResult };
                }
            });

            if (result.success) {
                console.log(`✅ 短信任务处理成功: ${task.notification_type} - ${task.order_no}`);
            } else {
                console.log(`⚠️ 短信任务处理失败，已记录重试: ${task.notification_type} - ${task.order_no}`);
            }

        } catch (error) {
            console.error(`❌ 处理短信任务异常: ${task.notification_type} - ${task.order_no}`, error);
            // 事务已回滚，这里只需要记录错误日志
            try {
                await this.handleTaskFailure(task, error.message);
            } catch (handleError) {
                console.error(`❌ 处理任务失败状态时出错:`, handleError);
            }
        }
    }

    /**
     * 处理任务失败
     * @param {Object} task 任务对象
     * @param {string} errorMsg 错误信息
     */
    async handleTaskFailure(task, errorMsg) {
        try {
            const newRetryCount = task.retry_count + 1;

            if (newRetryCount >= this.maxRetryCount) {
                // 超过最大重试次数，标记为失败
                await this.updateTaskStatus(task.id, 'failed', errorMsg);
                console.log(`❌ 短信任务最终失败: ${task.notification_type} - ${task.order_no}`);
            } else {
                // 增加重试次数，重新排队
                await this.updateTaskRetry(task.id, newRetryCount, errorMsg);
                console.log(`🔄  短信任务重试 ${newRetryCount}/${this.maxRetryCount}: ${task.notification_type} - ${task.order_no}`);
            }
        } catch (error) {
            console.error('❌ 处理任务失败状态失败:', error);
        }
    }

    /**
     * 使用事务连接处理任务失败
     * @param {Object} connection 数据库连接
     * @param {Object} task 任务对象
     * @param {string} errorMsg 错误信息
     */
    async handleTaskFailureWithConnection(connection, task, errorMsg) {
        try {
            const newRetryCount = task.retry_count + 1;

            if (newRetryCount >= this.maxRetryCount) {
                // 超过最大重试次数，标记为失败
                await this.updateTaskStatusWithConnection(connection, task.id, 'failed', errorMsg);
                console.log(`❌ 短信任务最终失败: ${task.notification_type} - ${task.order_no}`);
            } else {
                // 增加重试次数，重新排队
                await this.updateTaskRetryWithConnection(connection, task.id, newRetryCount, errorMsg);
                console.log(`🔄 短信任务重试 ${newRetryCount}/${this.maxRetryCount}: ${task.notification_type} - ${task.order_no}`);
            }
        } catch (error) {
            console.error('❌ 使用事务处理任务失败状态失败:', error);
            throw error; // 重新抛出错误以触发事务回滚
        }
    }

    /**
     * 更新任务状态
     * @param {number} taskId 任务ID
     * @param {string} status 状态
     * @param {string} errorMsg 错误信息
     * @param {string} messageId 消息ID
     */
    async updateTaskStatus(taskId, status, errorMsg = null, messageId = null) {
        try {
            const sql = `
                UPDATE sms_notification_queue
                SET status = ?, error_msg = ?, message_id = ?,
                    sent_time = CASE WHEN ? = 'completed' THEN NOW() ELSE sent_time END,
                    updated_at = NOW()
                WHERE id = ?
            `;

            await query(sql, [status, errorMsg, messageId, status, taskId]);
        } catch (error) {
            console.error('❌ 更新任务状态失败:', error);
        }
    }

    /**
     * 使用事务连接更新任务状态
     * @param {Object} connection 数据库连接
     * @param {number} taskId 任务ID
     * @param {string} status 状态
     * @param {string} errorMsg 错误信息
     * @param {string} messageId 消息ID
     */
    async updateTaskStatusWithConnection(connection, taskId, status, errorMsg = null, messageId = null) {
        try {
            const sql = `
                UPDATE sms_notification_queue
                SET status = ?, error_msg = ?, message_id = ?,
                    sent_time = CASE WHEN ? = 'completed' THEN NOW() ELSE sent_time END,
                    updated_at = NOW()
                WHERE id = ?
            `;

            await connection.execute(sql, [status, errorMsg, messageId, status, taskId]);
        } catch (error) {
            console.error('❌ 使用事务更新任务状态失败:', error);
            throw error; // 重新抛出错误以触发事务回滚
        }
    }

    /**
     * 更新任务重试信息
     * @param {number} taskId 任务ID
     * @param {number} retryCount 重试次数
     * @param {string} errorMsg 错误信息
     */
    async updateTaskRetry(taskId, retryCount, errorMsg) {
        try {
            const sql = `
                UPDATE sms_notification_queue
                SET status = 'pending', retry_count = ?, error_msg = ?, updated_at = NOW()
                WHERE id = ?
            `;

            await query(sql, [retryCount, errorMsg, taskId]);
        } catch (error) {
            console.error('❌ 更新任务重试信息失败:', error);
        }
    }

    /**
     * 使用事务连接更新任务重试信息
     * @param {Object} connection 数据库连接
     * @param {number} taskId 任务ID
     * @param {number} retryCount 重试次数
     * @param {string} errorMsg 错误信息
     */
    async updateTaskRetryWithConnection(connection, taskId, retryCount, errorMsg) {
        try {
            const sql = `
                UPDATE sms_notification_queue
                SET status = 'pending', retry_count = ?, error_msg = ?, updated_at = NOW()
                WHERE id = ?
            `;

            await connection.execute(sql, [retryCount, errorMsg, taskId]);
        } catch (error) {
            console.error('❌ 使用事务更新任务重试信息失败:', error);
            throw error; // 重新抛出错误以触发事务回滚
        }
    }

    /**
     * 添加延迟发货通知到队列
     * @param {string} orderNo 订单号
     * @param {string} factoryName 工厂名称
     * @param {string} followerName 跟单员姓名
     * @param {number} delayDays 延迟天数
     */
    async addDelayedShippingNotification(orderNo, factoryName, followerName, delayDays) {
        try {
            // 获取跟单员手机号
            const phoneNumber = await this.smsNotificationService.getFollowerPhone(followerName);
            if (!phoneNumber) {
                console.log(`⚠️ 跟单员 ${followerName} 未配置手机号，跳过添加延迟通知`);
                return false;
            }

            // 使用统一的重复检查服务
            const alreadySent = await this.duplicateCheckService.checkDelayedShippingDuplicate(orderNo, followerName, delayDays);
            if (alreadySent) {
                console.log(`⚠️ 已发送过 ${delayDays} 天延迟通知: ${orderNo}`);
                return false;
            }

            // 获取订单创建时间
            const orderInfo = await this.smsNotificationService.getOrderInfo(orderNo, factoryName);
            if (!orderInfo) {
                console.log(`⚠️ 未找到订单信息: ${orderNo}`);
                return false;
            }

            // 添加到队列
            const sql = `
                INSERT INTO sms_notification_queue (
                    order_no, factory_name, follower_name, phone_number, 
                    notification_type, template_code, template_params, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `;

            const templateParams = {
                follower: followerName,
                orderNo: orderNo,
                delayDays: delayDays.toString(),
                customerName: factoryName,
                orderTime: this.smsNotificationService.formatDateTime(orderInfo.created_at)
            };

            await query(sql, [
                orderNo, factoryName, followerName, phoneNumber,
                'delayed_shipping', 'SMS_493420157', 
                JSON.stringify(templateParams), 'pending'
            ]);

            console.log(`📋 已添加延迟发货通知到队列: ${orderNo} (${delayDays}天)`);
            return true;

        } catch (error) {
            console.error('❌ 添加延迟发货通知失败:', error);
            return false;
        }
    }

    /**
     * 获取队列统计信息
     * @returns {Promise<Object>} 统计信息
     */
    async getQueueStats() {
        try {
            const sql = `
                SELECT 
                    status,
                    notification_type,
                    COUNT(*) as count
                FROM sms_notification_queue
                GROUP BY status, notification_type
                ORDER BY status, notification_type
            `;
            
            const results = await query(sql);
            
            const stats = {
                total: 0,
                pending: 0,
                processing: 0,
                completed: 0,
                failed: 0,
                byType: {}
            };

            results.forEach(row => {
                stats.total += row.count;
                stats[row.status] = (stats[row.status] || 0) + row.count;
                
                if (!stats.byType[row.notification_type]) {
                    stats.byType[row.notification_type] = {};
                }
                stats.byType[row.notification_type][row.status] = row.count;
            });

            return stats;
        } catch (error) {
            console.error('❌ 获取队列统计失败:', error);
            return null;
        }
    }

    /**
     * 清理已完成的任务
     * @param {number} daysOld 保留天数
     */
    async cleanupCompletedTasks(daysOld = 7) {
        try {
            const sql = `
                DELETE FROM sms_notification_queue 
                WHERE status IN ('completed', 'failed') 
                AND updated_at < DATE_SUB(NOW(), INTERVAL ? DAY)
            `;
            
            const result = await query(sql, [daysOld]);
            console.log(`🧹 清理了 ${result.affectedRows} 个已完成的短信任务`);
            return result.affectedRows;
        } catch (error) {
            console.error('❌ 清理已完成任务失败:', error);
            return 0;
        }
    }

    /**
     * 延迟函数
     * @param {number} ms 毫秒
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = SMSQueueService;
