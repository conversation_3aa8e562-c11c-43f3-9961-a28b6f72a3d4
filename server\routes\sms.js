/**
 * 短信管理路由
 * 处理短信发送、队列管理和统计功能
 */

const express = require('express');
const router = express.Router();
const serviceManager = require('../utils/serviceManager');
const { verifyToken } = require('./auth');
const { query } = require('../config/database');

// 获取服务实例
const getSMSNotificationService = () => serviceManager.getSMSNotificationService();
const getSMSQueueService = () => serviceManager.getSMSQueueService();
const getDelayedShippingChecker = () => serviceManager.getDelayedShippingChecker();

/**
 * 获取短信队列统计信息
 * GET /api/sms/stats
 */
router.get('/stats', verifyToken, async (req, res) => {
    try {
        const queueStats = await getSMSQueueService().getQueueStats();
        const delayedStats = await getDelayedShippingChecker().getDelayedShippingStats();
        
        res.json({
            success: true,
            message: '获取统计信息成功',
            data: {
                queue: queueStats,
                delayed: delayedStats
            }
        });
    } catch (error) {
        console.error('❌ 获取短信统计失败:', error.message);
        res.status(500).json({
            success: false,
            message: '获取统计信息失败: ' + error.message
        });
    }
});

/**
 * 获取短信发送历史
 * GET /api/sms/history
 */
router.get('/history', verifyToken, async (req, res) => {
    try {
        const { page = 1, limit = 20, follower_name, order_no, notification_type } = req.query;
        const offset = (page - 1) * limit;
        
        let whereClauses = [];
        let params = [];
        
        if (follower_name) {
            whereClauses.push('follower_name = ?');
            params.push(follower_name);
        }
        
        if (order_no) {
            whereClauses.push('order_no LIKE ?');
            params.push(`%${order_no}%`);
        }
        
        if (notification_type) {
            whereClauses.push('notification_type = ?');
            params.push(notification_type);
        }
        
        const whereClause = whereClauses.length > 0 ? 'WHERE ' + whereClauses.join(' AND ') : '';
        
        // 查询总数
        const countSql = `
            SELECT COUNT(*) as total
            FROM sms_logs
            ${whereClause}
        `;
        const countResult = await query(countSql, params);
        const total = countResult[0].total;
        
        // 查询数据
        const dataSql = `
            SELECT id, phone_numbers, template_code, order_no, follower_name, 
                   notification_type, send_status, response_message, send_time
            FROM sms_logs
            ${whereClause}
            ORDER BY send_time DESC
            LIMIT ? OFFSET ?
        `;
        const dataResult = await query(dataSql, [...params, parseInt(limit), offset]);
        
        res.json({
            success: true,
            message: '获取短信历史成功',
            data: {
                records: dataResult,
                total: total,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(total / limit)
            }
        });
        
    } catch (error) {
        console.error('❌ 获取短信历史失败:', error.message);
        res.status(500).json({
            success: false,
            message: '获取短信历史失败: ' + error.message
        });
    }
});

/**
 * 手动发送订单生成通知
 * POST /api/sms/send-order-created
 */
router.post('/send-order-created', verifyToken, async (req, res) => {
    try {
        const { orderNo, factoryName, followerName } = req.body;
        
        if (!orderNo || !factoryName || !followerName) {
            return res.status(400).json({
                success: false,
                message: '订单号、工厂名称和跟单员姓名不能为空'
            });
        }
        
        const result = await getSMSNotificationService().sendOrderCreatedNotification(
            orderNo, factoryName, followerName
        );
        
        if (result.success) {
            res.json({
                success: true,
                message: '订单生成通知发送成功',
                data: { messageId: result.messageId }
            });
        } else {
            res.status(400).json({
                success: false,
                message: '发送失败: ' + result.error
            });
        }
        
    } catch (error) {
        console.error('❌ 手动发送订单生成通知失败:', error.message);
        res.status(500).json({
            success: false,
            message: '发送订单生成通知失败: ' + error.message
        });
    }
});

/**
 * 手动发送延迟发货通知
 * POST /api/sms/send-delayed-shipping
 */
router.post('/send-delayed-shipping', verifyToken, async (req, res) => {
    try {
        const { orderNo, factoryName, followerName, delayDays } = req.body;
        
        if (!orderNo || !factoryName || !followerName || !delayDays) {
            return res.status(400).json({
                success: false,
                message: '订单号、工厂名称、跟单员姓名和延迟天数不能为空'
            });
        }
        
        const result = await getSMSNotificationService().sendDelayedShippingNotification(
            orderNo, factoryName, followerName, parseInt(delayDays)
        );
        
        if (result.success) {
            res.json({
                success: true,
                message: '延迟发货通知发送成功',
                data: { messageId: result.messageId }
            });
        } else {
            res.status(400).json({
                success: false,
                message: '发送失败: ' + result.error
            });
        }
        
    } catch (error) {
        console.error('❌ 手动发送延迟发货通知失败:', error.message);
        res.status(500).json({
            success: false,
            message: '发送延迟发货通知失败: ' + error.message
        });
    }
});

/**
 * 检查特定订单的延迟状态
 * POST /api/sms/check-order-delay
 */
router.post('/check-order-delay', verifyToken, async (req, res) => {
    try {
        const { orderNo, factoryName } = req.body;
        
        if (!orderNo || !factoryName) {
            return res.status(400).json({
                success: false,
                message: '订单号和工厂名称不能为空'
            });
        }
        
        const result = await getDelayedShippingChecker().checkSpecificOrder(orderNo, factoryName);
        
        if (result) {
            res.json({
                success: true,
                message: '订单延迟检查完成',
                data: result
            });
        } else {
            res.status(404).json({
                success: false,
                message: '未找到订单或检查失败'
            });
        }
        
    } catch (error) {
        console.error('❌ 检查订单延迟失败:', error.message);
        res.status(500).json({
            success: false,
            message: '检查订单延迟失败: ' + error.message
        });
    }
});

/**
 * 获取跟单员列表（带手机号）
 * GET /api/sms/followers
 */
router.get('/followers', verifyToken, async (req, res) => {
    try {
        const sql = `
            SELECT id, username, follower_name, phone, position, allowed, status
            FROM follower_login
            WHERE status = 1
            ORDER BY follower_name
        `;

        const followers = await query(sql);

        res.json({
            success: true,
            message: '获取跟单员列表成功',
            data: followers
        });

    } catch (error) {
        console.error('❌ 获取跟单员列表失败:', error.message);
        res.status(500).json({
            success: false,
            message: '获取跟单员列表失败: ' + error.message
        });
    }
});

/**
 * 获取当前跟单员的短信通知设置
 * GET /api/sms/notification-settings
 */
router.get('/notification-settings', verifyToken, async (req, res) => {
    try {
        const username = req.user.username;

        const sql = `
            SELECT id, username, follower_name, phone, position, allowed
            FROM follower_login
            WHERE username = ? AND status = 1
        `;

        const followers = await query(sql, [username]);

        if (followers.length === 0) {
            return res.status(404).json({
                success: false,
                message: '跟单员用户不存在'
            });
        }

        const follower = followers[0];

        res.json({
            success: true,
            message: '获取短信通知设置成功',
            data: {
                id: follower.id,
                follower_name: follower.follower_name,
                phone: follower.phone,
                allowed: follower.allowed === 1,
                hasPhone: !!follower.phone
            }
        });

    } catch (error) {
        console.error('❌ 获取短信通知设置失败:', error.message);
        res.status(500).json({
            success: false,
            message: '获取短信通知设置失败: ' + error.message
        });
    }
});

/**
 * 更新跟单员手机号
 * PUT /api/sms/followers/:id/phone
 */
router.put('/followers/:id/phone', verifyToken, async (req, res) => {
    try {
        const { id } = req.params;
        const { phone } = req.body;

        if (!phone) {
            return res.status(400).json({
                success: false,
                message: '手机号不能为空'
            });
        }

        // 验证手机号格式
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(phone)) {
            return res.status(400).json({
                success: false,
                message: '手机号格式不正确'
            });
        }

        const sql = `
            UPDATE follower_login
            SET phone = ?, updated_at = NOW()
            WHERE id = ? AND status = 1
        `;

        const result = await query(sql, [phone, id]);

        if (result.affectedRows > 0) {
            res.json({
                success: true,
                message: '手机号更新成功'
            });
        } else {
            res.status(404).json({
                success: false,
                message: '跟单员不存在或更新失败'
            });
        }

    } catch (error) {
        console.error('❌ 更新手机号失败:', error.message);
        res.status(500).json({
            success: false,
            message: '更新手机号失败: ' + error.message
        });
    }
});

/**
 * 更新当前跟单员的短信通知设置
 * PUT /api/sms/notification-settings
 */
router.put('/notification-settings', verifyToken, async (req, res) => {
    try {
        const username = req.user.username;
        const { phone, allowed } = req.body;

        // 验证手机号格式（如果提供了手机号）
        if (phone) {
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(phone)) {
                return res.status(400).json({
                    success: false,
                    message: '手机号格式不正确'
                });
            }
        }

        // 构建更新SQL
        let updateFields = [];
        let updateValues = [];

        if (phone !== undefined) {
            updateFields.push('phone = ?');
            updateValues.push(phone);
        }

        if (allowed !== undefined) {
            updateFields.push('allowed = ?');
            updateValues.push(allowed ? 1 : 0);
        }

        if (updateFields.length === 0) {
            return res.status(400).json({
                success: false,
                message: '没有提供要更新的字段'
            });
        }

        updateFields.push('updated_at = NOW()');
        updateValues.push(username);

        const sql = `
            UPDATE follower_login
            SET ${updateFields.join(', ')}
            WHERE username = ? AND status = 1
        `;

        const result = await query(sql, updateValues);

        if (result.affectedRows > 0) {
            res.json({
                success: true,
                message: '短信通知设置更新成功'
            });
        } else {
            res.status(404).json({
                success: false,
                message: '跟单员用户不存在或更新失败'
            });
        }
        
    } catch (error) {
        console.error('❌ 更新跟单员手机号失败:', error.message);
        res.status(500).json({
            success: false,
            message: '更新手机号失败: ' + error.message
        });
    }
});

/**
 * 清理已完成的短信任务
 * POST /api/sms/cleanup
 */
router.post('/cleanup', verifyToken, async (req, res) => {
    try {
        const { daysOld = 7 } = req.body;
        
        const cleanedCount = await getSMSQueueService().cleanupCompletedTasks(parseInt(daysOld));
        
        res.json({
            success: true,
            message: `清理任务完成，删除了 ${cleanedCount} 个已完成的任务`
        });
        
    } catch (error) {
        console.error('❌ 清理短信任务失败:', error.message);
        res.status(500).json({
            success: false,
            message: '清理任务失败: ' + error.message
        });
    }
});

module.exports = router;
